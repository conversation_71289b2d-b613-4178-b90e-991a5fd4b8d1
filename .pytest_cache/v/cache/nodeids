["tests/test_alternative_data.py::TestAlternativeDataEngine::test_alternative_data_report_generation", "tests/test_alternative_data.py::TestAlternativeDataEngine::test_company_fundamentals_analysis", "tests/test_alternative_data.py::TestAlternativeDataEngine::test_engine_initialization", "tests/test_alternative_data.py::TestAlternativeDataEngine::test_market_context_analysis", "tests/test_alternative_data.py::TestEconomicDataClient::test_economic_indicators_retrieval", "tests/test_alternative_data.py::TestEconomicDataClient::test_indicator_configuration", "tests/test_alternative_data.py::TestEconomicIndicator::test_economic_indicator_creation", "tests/test_alternative_data.py::TestIntegration::test_alternative_data_initialization", "tests/test_alternative_data.py::TestIntegration::test_end_to_end_analysis", "tests/test_alternative_data.py::TestIntegration::test_error_handling", "tests/test_alternative_data.py::TestIntegration::test_performance_requirements", "tests/test_alternative_data.py::TestPatentData::test_patent_data_creation", "tests/test_alternative_data.py::TestPatentDataClient::test_mock_patent_generation", "tests/test_alternative_data.py::TestPatentDataClient::test_patent_client_initialization", "tests/test_alternative_data.py::TestPatentDataClient::test_patent_search_by_assignee", "tests/test_alternative_data.py::TestSECDataClient::test_cik_lookup_format", "tests/test_alternative_data.py::TestSECDataClient::test_recent_filings_structure", "tests/test_alternative_data.py::TestSECDataClient::test_sec_client_initialization", "tests/test_alternative_data.py::TestSECFiling::test_sec_filing_creation", "tests/test_core_pipeline.py::TestDataValidation::test_extreme_price_movement", "tests/test_core_pipeline.py::TestDataValidation::test_invalid_price_relationships", "tests/test_core_pipeline.py::TestDataValidation::test_invalid_sentiment_range", "tests/test_core_pipeline.py::TestDataValidation::test_missing_required_fields", "tests/test_core_pipeline.py::TestDataValidation::test_negative_volume", "tests/test_core_pipeline.py::TestDataValidation::test_news_data_validation", "tests/test_core_pipeline.py::TestDataValidation::test_sentiment_data_validation", "tests/test_core_pipeline.py::TestDataValidation::test_valid_market_data", "tests/test_core_pipeline.py::TestIntegration::test_end_to_end_data_flow", "tests/test_core_pipeline.py::TestIntegration::test_performance_requirements", "tests/test_core_pipeline.py::TestMarketDataPipeline::test_market_data_point_creation", "tests/test_core_pipeline.py::TestMarketDataPipeline::test_pipeline_initialization", "tests/test_core_pipeline.py::TestMarketDataPipeline::test_pipeline_status", "tests/test_core_pipeline.py::TestMarketDataPipeline::test_polygon_client_initialization", "tests/test_core_pipeline.py::TestMarketDataPipeline::test_symbol_data_ingestion", "tests/test_core_pipeline.py::TestRateLimiter::test_budget_tracking", "tests/test_core_pipeline.py::TestRateLimiter::test_rate_limit_acquisition", "tests/test_core_pipeline.py::TestRateLimiter::test_rate_limit_reset", "tests/test_core_pipeline.py::test_api_key_validation", "tests/test_core_pipeline.py::test_configuration_validation", "tests/test_monitoring_dashboard.py::TestAnomalyDetector::test_anomaly_detection_normal_data", "tests/test_monitoring_dashboard.py::TestAnomalyDetector::test_anomaly_detection_outlier", "tests/test_monitoring_dashboard.py::TestAnomalyDetector::test_insufficient_data", "tests/test_monitoring_dashboard.py::TestAnomalyDetector::test_statistics_calculation", "tests/test_monitoring_dashboard.py::TestAnomalyDetector::test_window_size_limit", "tests/test_monitoring_dashboard.py::TestAnomalyDetector::test_zero_variance_data", "tests/test_monitoring_dashboard.py::TestDataQualityAlert::test_alert_creation", "tests/test_monitoring_dashboard.py::TestDataQualityMonitor::test_api_performance_recording", "tests/test_monitoring_dashboard.py::TestDataQualityMonitor::test_data_quality_recording", "tests/test_monitoring_dashboard.py::TestDataQualityMonitor::test_data_quality_report", "tests/test_monitoring_dashboard.py::TestDataQualityMonitor::test_low_quality_alert_creation", "tests/test_monitoring_dashboard.py::TestDataQualityMonitor::test_system_health_retrieval", "tests/test_monitoring_dashboard.py::TestIntegration::test_anomaly_detection_integration", "tests/test_monitoring_dashboard.py::TestIntegration::test_end_to_end_monitoring", "tests/test_monitoring_dashboard.py::TestIntegration::test_performance_requirements", "tests/test_monitoring_dashboard.py::TestSystemMetrics::test_system_metrics_creation", "tests/test_risk_analytics.py::TestCVaRCalculator::test_extreme_returns", "tests/test_risk_analytics.py::TestCVaRCalculator::test_insufficient_data", "tests/test_risk_analytics.py::TestCVaRCalculator::test_monte_carlo_simulation", "tests/test_risk_analytics.py::TestCVaRCalculator::test_var_cvar_calculation", "tests/test_risk_analytics.py::TestIntegration::test_numerical_stability", "tests/test_risk_analytics.py::TestIntegration::test_performance_requirements", "tests/test_risk_analytics.py::TestIntegration::test_pytorch_mps_availability", "tests/test_risk_analytics.py::TestIntegration::test_risk_analytics_initialization", "tests/test_risk_analytics.py::TestPortfolioRiskAnalyzer::test_default_risk_metrics", "tests/test_risk_analytics.py::TestPortfolioRiskAnalyzer::test_portfolio_stress_test_structure", "tests/test_risk_analytics.py::TestPortfolioRiskAnalyzer::test_risk_metrics_structure", "tests/test_risk_analytics.py::TestRegimeDetector::test_feature_preparation", "tests/test_risk_analytics.py::TestRegimeDetector::test_insufficient_data_handling", "tests/test_risk_analytics.py::TestRegimeDetector::test_model_fitting", "tests/test_risk_analytics.py::TestRegimeDetector::test_prediction_without_fitting", "tests/test_risk_analytics.py::TestRegimeDetector::test_regime_prediction", "tests/test_sentiment_engine.py::TestIntegration::test_end_to_end_sentiment_analysis", "tests/test_sentiment_engine.py::TestIntegration::test_performance_requirements", "tests/test_sentiment_engine.py::TestNewsDataClient::test_client_initialization", "tests/test_sentiment_engine.py::TestNewsDataClient::test_news_article_creation", "tests/test_sentiment_engine.py::TestRedditSentimentClient::test_reddit_initialization", "tests/test_sentiment_engine.py::TestRedditSentimentClient::test_subreddit_sentiment_mock", "tests/test_sentiment_engine.py::TestSentimentEngine::test_engine_initialization", "tests/test_sentiment_engine.py::TestSentimentEngine::test_score_to_label_conversion", "tests/test_sentiment_engine.py::TestSentimentEngine::test_sentiment_summary", "tests/test_sentiment_engine.py::TestSentimentEngine::test_symbol_sentiment_analysis", "tests/test_sentiment_engine.py::TestSentimentEngine::test_text_cleaning", "tests/test_sentiment_engine.py::TestSentimentEngine::test_text_sentiment_analysis", "tests/test_sentiment_engine.py::TestSentimentResult::test_sentiment_result_creation", "tests/test_sentiment_engine.py::TestVADERSentimentAnalyzer::test_empty_text", "tests/test_sentiment_engine.py::TestVADERSentimentAnalyzer::test_financial_lexicon", "tests/test_sentiment_engine.py::TestVADERSentimentAnalyzer::test_negative_sentiment", "tests/test_sentiment_engine.py::TestVADERSentimentAnalyzer::test_neutral_sentiment", "tests/test_sentiment_engine.py::TestVADERSentimentAnalyzer::test_positive_sentiment"]