{"timestamp": "2025-07-03T02:01:19.701429", "blueprint_compliance": {"data_engine": {"financial_data": {"description": "yfinance integration", "implemented": true, "compliance_score": 1.0}, "sentiment_analysis": {"description": "VADER sentiment analyzer", "implemented": false, "compliance_score": 0.0}, "data_storage": {"description": "SQLite/DuckDB storage", "implemented": false, "compliance_score": 0.0}, "data_fusion": {"description": "Multi-source data integration", "implemented": true, "compliance_score": 1.0}}, "risk_model": {"volatility_prediction": {"description": "Risk model implementation", "implemented": true, "compliance_score": 1.0}, "portfolio_scoring": {"description": "Portfolio risk scoring", "implemented": true, "compliance_score": 1.0}, "real_time_processing": {"description": "Real-time analysis capability", "implemented": true, "compliance_score": 1.0}}, "interface": {"streamlit_dashboard": {"description": "Web dashboard", "implemented": true, "compliance_score": 1.0}, "user_input": {"description": "Portfolio input capability", "implemented": false, "compliance_score": 0.0}, "risk_display": {"description": "Risk score visualization", "implemented": true, "compliance_score": 1.0}}, "budget_constraints": {"free_apis": {"description": "Zero-cost API usage", "implemented": true, "compliance_score": 1.0}, "m1_max_optimization": {"description": "Local processing on M1 Max", "implemented": true, "compliance_score": 1.0}, "open_source_models": {"description": "No paid model dependencies", "implemented": true, "compliance_score": 1.0}}, "overall_score": 0.7692307692307693, "total_requirements": 13, "implemented_count": 10}, "performance_metrics": {"validation_throughput": {"measured": 117298.25267915073, "claimed": 115835, "variance": 0.012632215471582218, "realistic": true}, "data_ingestion": {"processing_time": 0.6117029190063477, "quality_score": 1.0, "realistic": true}, "sentiment_analysis": {"processing_time": 1.1260511875152588, "realistic": true, "data_quality": true}}, "data_quality_assessment": {"validation_accuracy": {"valid_data_passed": false, "invalid_data_rejected": true, "valid_quality_score": 0.8, "invalid_quality_score": 0.4, "accuracy_test_passed": false}, "monitoring_system": {"operational": true, "health_data": {"timestamp": "2025-07-03T02:01:21.553721", "metrics": {}, "api_performance": {}, "active_alerts": 0, "alert_summary": {"critical": 0, "high": 0, "medium": 0, "low": 0}}}}, "production_readiness": {"error_handling": {"score": 0.6666666666666666, "tests_passed": 2, "total_tests": 3}, "api_configuration": {"score": 0.5, "configured_apis": 2, "total_apis": 4}, "security": {"score": 1.0, "checks_passed": 2, "total_checks": 2}, "overall_score": 0.7166666666666666}, "critical_issues": ["Blueprint compliance below 95% threshold: 76.9%", "Data validation accuracy test failed", "Production readiness below 95% threshold: 71.7%"], "recommendations": ["Complete missing blueprint requirements before production deployment", "Improve data validation accuracy to meet quality standards", "Address production readiness gaps in error handling and security", "Resolve all critical issues before proceeding to live trading", "❌ System does NOT meet 95% confidence threshold - Additional development required"], "final_assessment": {"overall_score": 0.621474358974359, "component_scores": {"blueprint_compliance": 0.7692307692307693, "performance_realistic": 1.0, "data_quality": 0.0, "production_readiness": 0.7166666666666666}, "critical_issues_count": 3, "meets_95_percent_threshold": false}}