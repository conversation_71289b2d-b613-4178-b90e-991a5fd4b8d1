"""
QuantumEdge Financial Intelligence Tool - Alternative Data Integration
SEC filings analysis, patent tracking, and economic indicators
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import aiohttp
import json
import re
from dataclasses import dataclass
import xml.etree.ElementTree as ET

from src.config import config
from src.rate_limiter import rate_limiter
from src.sentiment_engine import sentiment_engine
from src.data_validation import DataValidator

logger = logging.getLogger('quantumedge.alternative_data')

@dataclass
class SECFiling:
    """SEC filing data structure"""
    company_name: str
    cik: str
    form_type: str
    filing_date: datetime
    report_date: datetime
    document_url: str
    summary: str
    sentiment_score: Optional[float] = None
    
    def to_dict(self) -> Dict:
        return {
            'company_name': self.company_name,
            'cik': self.cik,
            'form_type': self.form_type,
            'filing_date': self.filing_date.isoformat(),
            'report_date': self.report_date.isoformat(),
            'document_url': self.document_url,
            'summary': self.summary,
            'sentiment_score': self.sentiment_score
        }

@dataclass
class PatentData:
    """Patent data structure"""
    patent_number: str
    title: str
    assignee: str
    filing_date: datetime
    grant_date: Optional[datetime]
    abstract: str
    classification: str
    innovation_score: Optional[float] = None
    
    def to_dict(self) -> Dict:
        return {
            'patent_number': self.patent_number,
            'title': self.title,
            'assignee': self.assignee,
            'filing_date': self.filing_date.isoformat(),
            'grant_date': self.grant_date.isoformat() if self.grant_date else None,
            'abstract': self.abstract,
            'classification': self.classification,
            'innovation_score': self.innovation_score
        }

@dataclass
class EconomicIndicator:
    """Economic indicator data structure"""
    indicator_name: str
    value: float
    timestamp: datetime
    source: str
    unit: str
    frequency: str  # 'daily', 'weekly', 'monthly', 'quarterly'
    
    def to_dict(self) -> Dict:
        return {
            'indicator_name': self.indicator_name,
            'value': self.value,
            'timestamp': self.timestamp.isoformat(),
            'source': self.source,
            'unit': self.unit,
            'frequency': self.frequency
        }

class SECDataClient:
    """SEC EDGAR API client for filings analysis"""
    
    def __init__(self):
        self.base_url = "https://data.sec.gov"
        self.session = None
        self.headers = {
            'User-Agent': 'QuantumEdge Financial Intelligence Tool (<EMAIL>)'
        }
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(headers=self.headers)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_company_cik(self, ticker: str) -> Optional[str]:
        """Get CIK (Central Index Key) for a company ticker"""
        try:
            # Check rate limits
            can_proceed, rate_info = await rate_limiter.acquire('sec')
            if not can_proceed:
                await asyncio.sleep(0.1)  # SEC allows 10 requests per second
            
            url = f"{self.base_url}/api/xbrl/companyfacts/CIK{ticker}.json"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('cik')
                else:
                    # Try ticker lookup
                    return await self._lookup_ticker_cik(ticker)
                    
        except Exception as e:
            logger.warning(f"Error getting CIK for {ticker}: {e}")
            return None
    
    async def _lookup_ticker_cik(self, ticker: str) -> Optional[str]:
        """Lookup CIK using company tickers endpoint"""
        try:
            url = f"{self.base_url}/files/company_tickers.json"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Search for ticker in the data
                    for entry in data.values():
                        if entry.get('ticker', '').upper() == ticker.upper():
                            return str(entry.get('cik_str', '')).zfill(10)
                    
        except Exception as e:
            logger.warning(f"Error in ticker lookup for {ticker}: {e}")
        
        return None
    
    async def get_recent_filings(self, cik: str, form_types: List[str] = None, 
                               limit: int = 10) -> List[SECFiling]:
        """Get recent SEC filings for a company"""
        if form_types is None:
            form_types = ['10-K', '10-Q', '8-K']
        
        try:
            can_proceed, rate_info = await rate_limiter.acquire('sec')
            if not can_proceed:
                await asyncio.sleep(0.1)
            
            # Ensure CIK is properly formatted
            formatted_cik = str(cik).zfill(10)
            url = f"{self.base_url}/api/xbrl/companyfacts/CIK{formatted_cik}.json"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return await self._parse_filings_data(data, form_types, limit)
                else:
                    logger.warning(f"SEC API error {response.status} for CIK {cik}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error fetching SEC filings for CIK {cik}: {e}")
            return []
    
    async def _parse_filings_data(self, data: Dict, form_types: List[str], 
                                limit: int) -> List[SECFiling]:
        """Parse SEC filings data"""
        filings = []
        
        try:
            # Extract company information
            company_name = data.get('entityName', 'Unknown')
            cik = data.get('cik', '')
            
            # For demo purposes, create mock filings since the actual SEC API
            # structure is complex and requires additional endpoints
            mock_filings = [
                {
                    'form_type': '10-K',
                    'filing_date': datetime.utcnow() - timedelta(days=30),
                    'report_date': datetime.utcnow() - timedelta(days=90),
                    'summary': f'{company_name} annual report showing strong financial performance'
                },
                {
                    'form_type': '10-Q',
                    'filing_date': datetime.utcnow() - timedelta(days=15),
                    'report_date': datetime.utcnow() - timedelta(days=45),
                    'summary': f'{company_name} quarterly report with revenue growth'
                },
                {
                    'form_type': '8-K',
                    'filing_date': datetime.utcnow() - timedelta(days=5),
                    'report_date': datetime.utcnow() - timedelta(days=5),
                    'summary': f'{company_name} current report on material events'
                }
            ]
            
            for filing_data in mock_filings[:limit]:
                if filing_data['form_type'] in form_types:
                    filing = SECFiling(
                        company_name=company_name,
                        cik=str(cik),
                        form_type=filing_data['form_type'],
                        filing_date=filing_data['filing_date'],
                        report_date=filing_data['report_date'],
                        document_url=f"https://www.sec.gov/Archives/edgar/data/{cik}/",
                        summary=filing_data['summary']
                    )
                    filings.append(filing)
            
        except Exception as e:
            logger.error(f"Error parsing SEC filings data: {e}")
        
        return filings

class PatentDataClient:
    """USPTO Patent API client for innovation tracking"""
    
    def __init__(self):
        self.base_url = "https://developer.uspto.gov"
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def search_patents_by_assignee(self, assignee: str, 
                                       limit: int = 20) -> List[PatentData]:
        """Search patents by assignee (company name)"""
        try:
            # USPTO API has rate limits, but they're generous for basic usage
            url = f"{self.base_url}/api/v1/patent/application"
            
            # For demo purposes, create mock patent data
            # In production, this would use the actual USPTO API
            mock_patents = self._generate_mock_patents(assignee, limit)
            
            return mock_patents
            
        except Exception as e:
            logger.error(f"Error searching patents for {assignee}: {e}")
            return []
    
    def _generate_mock_patents(self, assignee: str, limit: int) -> List[PatentData]:
        """Generate mock patent data for demonstration"""
        patents = []
        
        patent_templates = [
            {
                'title': 'Machine Learning System for Financial Analysis',
                'abstract': 'A system and method for analyzing financial data using machine learning algorithms to predict market trends and assess investment risks.',
                'classification': 'G06Q 40/00'
            },
            {
                'title': 'Blockchain-Based Transaction Processing',
                'abstract': 'A distributed ledger system for secure and efficient processing of financial transactions with enhanced privacy features.',
                'classification': 'G06Q 20/00'
            },
            {
                'title': 'Automated Risk Assessment Platform',
                'abstract': 'An automated platform for real-time assessment of financial risks using alternative data sources and predictive analytics.',
                'classification': 'G06Q 40/08'
            }
        ]
        
        for i in range(min(limit, len(patent_templates))):
            template = patent_templates[i % len(patent_templates)]
            
            patent = PatentData(
                patent_number=f"US{10000000 + i}",
                title=template['title'],
                assignee=assignee,
                filing_date=datetime.utcnow() - timedelta(days=365 + i * 30),
                grant_date=datetime.utcnow() - timedelta(days=180 + i * 30),
                abstract=template['abstract'],
                classification=template['classification'],
                innovation_score=0.7 + (i % 3) * 0.1  # Mock innovation score
            )
            patents.append(patent)
        
        return patents

class EconomicDataClient:
    """Economic indicators data client"""
    
    def __init__(self):
        self.indicators = {
            'GDP_GROWTH': {'source': 'BEA', 'unit': 'percent', 'frequency': 'quarterly'},
            'UNEMPLOYMENT_RATE': {'source': 'BLS', 'unit': 'percent', 'frequency': 'monthly'},
            'INFLATION_RATE': {'source': 'BLS', 'unit': 'percent', 'frequency': 'monthly'},
            'INTEREST_RATE': {'source': 'FED', 'unit': 'percent', 'frequency': 'daily'},
            'VIX': {'source': 'CBOE', 'unit': 'index', 'frequency': 'daily'}
        }
    
    async def get_economic_indicators(self) -> List[EconomicIndicator]:
        """Get current economic indicators"""
        indicators = []
        
        # For demo purposes, generate mock economic data
        # In production, this would integrate with FRED API, BLS API, etc.
        
        mock_data = {
            'GDP_GROWTH': 2.1,
            'UNEMPLOYMENT_RATE': 3.7,
            'INFLATION_RATE': 3.2,
            'INTEREST_RATE': 5.25,
            'VIX': 18.5
        }
        
        for indicator_name, value in mock_data.items():
            config = self.indicators[indicator_name]
            
            indicator = EconomicIndicator(
                indicator_name=indicator_name,
                value=value,
                timestamp=datetime.utcnow(),
                source=config['source'],
                unit=config['unit'],
                frequency=config['frequency']
            )
            indicators.append(indicator)
        
        return indicators

class AlternativeDataEngine:
    """Main alternative data integration engine"""
    
    def __init__(self):
        self.sec_client = None
        self.patent_client = None
        self.economic_client = EconomicDataClient()
        self.validator = DataValidator()
        
    async def analyze_company_fundamentals(self, ticker: str) -> Dict[str, Any]:
        """Comprehensive fundamental analysis using alternative data"""
        results = {
            'ticker': ticker,
            'timestamp': datetime.utcnow().isoformat(),
            'sec_filings': [],
            'patent_portfolio': [],
            'sentiment_analysis': {},
            'innovation_metrics': {}
        }
        
        try:
            # SEC filings analysis
            async with SECDataClient() as sec_client:
                cik = await sec_client.get_company_cik(ticker)
                
                if cik:
                    filings = await sec_client.get_recent_filings(cik, limit=5)
                    
                    # Analyze sentiment of filings
                    for filing in filings:
                        sentiment_result = await sentiment_engine._analyze_text_sentiment(
                            filing.summary, 'sec_filing', ticker
                        )
                        filing.sentiment_score = sentiment_result.sentiment_score
                    
                    results['sec_filings'] = [f.to_dict() for f in filings]
            
            # Patent analysis
            async with PatentDataClient() as patent_client:
                # Use ticker as company name for demo
                patents = await patent_client.search_patents_by_assignee(ticker, limit=10)
                results['patent_portfolio'] = [p.to_dict() for p in patents]
                
                # Calculate innovation metrics
                if patents:
                    avg_innovation_score = sum(p.innovation_score or 0 for p in patents) / len(patents)
                    recent_patents = len([p for p in patents if 
                                        (datetime.utcnow() - p.filing_date).days < 365])
                    
                    results['innovation_metrics'] = {
                        'total_patents': len(patents),
                        'recent_patents_12m': recent_patents,
                        'avg_innovation_score': avg_innovation_score,
                        'patent_velocity': recent_patents / max(len(patents), 1)
                    }
            
            # Sentiment analysis summary
            if results['sec_filings']:
                filing_sentiments = [f['sentiment_score'] for f in results['sec_filings'] 
                                   if f['sentiment_score'] is not None]
                
                if filing_sentiments:
                    results['sentiment_analysis'] = {
                        'avg_filing_sentiment': sum(filing_sentiments) / len(filing_sentiments),
                        'sentiment_trend': 'positive' if filing_sentiments[-1] > 0 else 'negative',
                        'sentiment_volatility': np.std(filing_sentiments) if len(filing_sentiments) > 1 else 0
                    }
            
        except Exception as e:
            logger.error(f"Error in fundamental analysis for {ticker}: {e}")
            results['error'] = str(e)
        
        return results
    
    async def get_market_context(self) -> Dict[str, Any]:
        """Get broader market context using economic indicators"""
        try:
            indicators = await self.economic_client.get_economic_indicators()
            
            context = {
                'timestamp': datetime.utcnow().isoformat(),
                'economic_indicators': [i.to_dict() for i in indicators],
                'market_regime_indicators': {},
                'risk_factors': []
            }
            
            # Analyze economic conditions
            for indicator in indicators:
                if indicator.indicator_name == 'VIX':
                    if indicator.value > 30:
                        context['risk_factors'].append('High market volatility (VIX > 30)')
                    elif indicator.value < 15:
                        context['risk_factors'].append('Low market volatility (complacency risk)')
                
                elif indicator.indicator_name == 'UNEMPLOYMENT_RATE':
                    if indicator.value > 6:
                        context['risk_factors'].append('High unemployment rate')
                    elif indicator.value < 3:
                        context['risk_factors'].append('Very low unemployment (potential overheating)')
                
                elif indicator.indicator_name == 'INFLATION_RATE':
                    if indicator.value > 4:
                        context['risk_factors'].append('High inflation rate')
                    elif indicator.value < 1:
                        context['risk_factors'].append('Low inflation (deflationary risk)')
            
            # Market regime assessment
            vix_indicator = next((i for i in indicators if i.indicator_name == 'VIX'), None)
            if vix_indicator:
                if vix_indicator.value < 20:
                    regime = 'low_volatility'
                elif vix_indicator.value < 30:
                    regime = 'moderate_volatility'
                else:
                    regime = 'high_volatility'
                
                context['market_regime_indicators']['volatility_regime'] = regime
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting market context: {e}")
            return {'error': str(e)}
    
    async def generate_alternative_data_report(self, symbols: List[str]) -> Dict[str, Any]:
        """Generate comprehensive alternative data report"""
        report = {
            'timestamp': datetime.utcnow().isoformat(),
            'symbols_analyzed': symbols,
            'company_analyses': {},
            'market_context': {},
            'summary_insights': []
        }
        
        try:
            # Analyze each company
            for symbol in symbols:
                company_analysis = await self.analyze_company_fundamentals(symbol)
                report['company_analyses'][symbol] = company_analysis
            
            # Get market context
            report['market_context'] = await self.get_market_context()
            
            # Generate summary insights
            insights = []
            
            # Innovation insights
            innovation_scores = []
            for symbol, analysis in report['company_analyses'].items():
                if 'innovation_metrics' in analysis and analysis['innovation_metrics']:
                    score = analysis['innovation_metrics'].get('avg_innovation_score', 0)
                    innovation_scores.append((symbol, score))
            
            if innovation_scores:
                top_innovator = max(innovation_scores, key=lambda x: x[1])
                insights.append(f"{top_innovator[0]} shows highest innovation score ({top_innovator[1]:.2f})")
            
            # Sentiment insights
            sentiment_scores = []
            for symbol, analysis in report['company_analyses'].items():
                if 'sentiment_analysis' in analysis and analysis['sentiment_analysis']:
                    score = analysis['sentiment_analysis'].get('avg_filing_sentiment', 0)
                    sentiment_scores.append((symbol, score))
            
            if sentiment_scores:
                most_positive = max(sentiment_scores, key=lambda x: x[1])
                insights.append(f"{most_positive[0]} has most positive SEC filing sentiment ({most_positive[1]:.2f})")
            
            report['summary_insights'] = insights
            
        except Exception as e:
            logger.error(f"Error generating alternative data report: {e}")
            report['error'] = str(e)
        
        return report

# Global alternative data engine
alternative_data_engine = AlternativeDataEngine()

async def initialize_alternative_data():
    """Initialize alternative data systems"""
    logger.info("Alternative data integration system initialized")
