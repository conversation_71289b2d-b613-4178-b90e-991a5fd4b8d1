"""
QuantumEdge Financial Intelligence Tool - Core Pipeline Tests
Comprehensive validation of data pipeline components
"""

import pytest
import asyncio
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock

# Add src to path for imports
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.config import config
from src.data_validation import DataValidator, ValidationSeverity
from src.rate_limiter import RateLimiter, BudgetTracker
from src.market_data_pipeline import MarketDataPipeline, PolygonDataClient, MarketDataPoint

class TestDataValidation:
    """Test data validation framework"""
    
    def setup_method(self):
        self.validator = DataValidator(quality_threshold=0.95)
    
    def test_valid_market_data(self):
        """Test validation of valid market data"""
        valid_data = {
            'open': 150.0,
            'high': 155.0,
            'low': 149.0,
            'close': 152.0,
            'volume': 1000000,
            'timestamp': datetime.utcnow().timestamp()
        }
        
        result = self.validator.validate_market_data(valid_data, 'AAPL')
        
        assert result.passed is True
        assert result.quality_score >= 0.95
        assert result.symbol == 'AAPL'
        assert result.data_type == 'market_data'
    
    def test_invalid_price_relationships(self):
        """Test detection of invalid OHLC relationships"""
        invalid_data = {
            'open': 150.0,
            'high': 145.0,  # High < Open (invalid)
            'low': 149.0,
            'close': 152.0,
            'volume': 1000000,
            'timestamp': datetime.utcnow().timestamp()
        }
        
        result = self.validator.validate_market_data(invalid_data, 'TEST')
        
        assert result.passed is False
        assert result.quality_score < 0.95
        
        # Check that price continuity check failed
        price_check = next(
            (check for check in result.checks if check.check_name == 'price_continuity'),
            None
        )
        assert price_check is not None
        assert price_check.score == 0.0
        assert price_check.severity == ValidationSeverity.ERROR
    
    def test_extreme_price_movement(self):
        """Test detection of extreme price movements"""
        extreme_data = {
            'open': 100.0,
            'high': 200.0,
            'low': 95.0,
            'close': 180.0,  # 80% increase (extreme)
            'volume': 1000000,
            'timestamp': datetime.utcnow().timestamp()
        }
        
        result = self.validator.validate_market_data(extreme_data, 'VOLATILE')
        
        # Should pass but with warning
        price_check = next(
            (check for check in result.checks if check.check_name == 'price_continuity'),
            None
        )
        assert price_check is not None
        assert price_check.severity == ValidationSeverity.WARNING
    
    def test_missing_required_fields(self):
        """Test detection of missing required fields"""
        incomplete_data = {
            'open': 150.0,
            'high': 155.0,
            # Missing 'low', 'close', 'volume'
            'timestamp': datetime.utcnow().timestamp()
        }
        
        result = self.validator.validate_market_data(incomplete_data, 'INCOMPLETE')
        
        assert result.passed is False
        
        # Check that required fields check failed
        fields_check = next(
            (check for check in result.checks if check.check_name == 'required_fields'),
            None
        )
        assert fields_check is not None
        assert fields_check.score == 0.0
        assert fields_check.severity == ValidationSeverity.CRITICAL
    
    def test_negative_volume(self):
        """Test detection of negative volume"""
        negative_volume_data = {
            'open': 150.0,
            'high': 155.0,
            'low': 149.0,
            'close': 152.0,
            'volume': -1000,  # Negative volume (invalid)
            'timestamp': datetime.utcnow().timestamp()
        }
        
        result = self.validator.validate_market_data(negative_volume_data, 'NEGVOL')
        
        volume_check = next(
            (check for check in result.checks if check.check_name == 'volume_validity'),
            None
        )
        assert volume_check is not None
        assert volume_check.score == 0.0
        assert volume_check.severity == ValidationSeverity.ERROR
    
    def test_news_data_validation(self):
        """Test news data validation"""
        news_data = [
            {
                'headline': 'Apple Reports Strong Q4 Earnings',
                'summary': 'Apple Inc. reported better than expected earnings for Q4 2024...',
                'url': 'https://example.com/news/1',
                'datetime': datetime.utcnow().timestamp()
            },
            {
                'headline': 'Tech Stocks Rally on Fed Decision',
                'summary': 'Technology stocks surged following the Federal Reserve decision...',
                'url': 'https://example.com/news/2',
                'datetime': datetime.utcnow().timestamp()
            }
        ]
        
        result = self.validator.validate_news_data(news_data, 'AAPL')
        
        assert result.passed is True
        assert result.quality_score >= 0.95
        assert result.record_count == 2
    
    def test_sentiment_data_validation(self):
        """Test sentiment data validation"""
        sentiment_data = {
            'sentiment_score': 0.75,
            'confidence': 0.85,
            'source': 'news'
        }
        
        result = self.validator.validate_sentiment_data(sentiment_data, 'AAPL')
        
        assert result.passed is True
        assert result.quality_score >= 0.95
    
    def test_invalid_sentiment_range(self):
        """Test detection of invalid sentiment score range"""
        invalid_sentiment = {
            'sentiment_score': 2.5,  # Out of range [-1, 1]
            'confidence': 0.85,
            'source': 'news'
        }
        
        result = self.validator.validate_sentiment_data(invalid_sentiment, 'TEST')
        
        assert result.passed is False
        
        range_check = next(
            (check for check in result.checks if check.check_name == 'sentiment_range'),
            None
        )
        assert range_check is not None
        assert range_check.score == 0.0

class TestRateLimiter:
    """Test rate limiting system"""
    
    def setup_method(self):
        self.rate_limiter = RateLimiter()
    
    @pytest.mark.asyncio
    async def test_rate_limit_acquisition(self):
        """Test basic rate limit token acquisition"""
        await self.rate_limiter.initialize()
        
        # Should be able to acquire token
        success, info = await self.rate_limiter.acquire('polygon')
        
        assert success is True
        assert info.api_name == 'polygon'
        assert info.requests_remaining >= 0
    
    @pytest.mark.asyncio
    async def test_budget_tracking(self):
        """Test budget tracking functionality"""
        budget_tracker = BudgetTracker()
        
        # Should be able to afford free requests
        can_afford = await budget_tracker.can_afford(0.0)
        assert can_afford is True
        
        # Record a free expense
        await budget_tracker.record_expense('polygon', 0.0)
        
        # Get budget status
        status = await budget_tracker.get_budget_status()
        assert 'monthly_budget' in status
        assert 'current_spend' in status
        assert 'remaining_budget' in status
    
    @pytest.mark.asyncio
    async def test_rate_limit_reset(self):
        """Test rate limit reset functionality"""
        await self.rate_limiter.initialize()
        
        # Reset limits for polygon
        await self.rate_limiter.reset_limits('polygon')
        
        # Should be able to acquire token after reset
        success, info = await self.rate_limiter.acquire('polygon')
        assert success is True

class TestMarketDataPipeline:
    """Test market data pipeline components"""
    
    def setup_method(self):
        self.pipeline = None
    
    @pytest.mark.asyncio
    async def test_polygon_client_initialization(self):
        """Test Polygon client initialization"""
        async with PolygonDataClient() as client:
            assert client.api_key is not None
            assert client.base_url == config.apis['polygon'].base_url
    
    @pytest.mark.asyncio
    async def test_market_data_point_creation(self):
        """Test MarketDataPoint creation and serialization"""
        data_point = MarketDataPoint(
            symbol='AAPL',
            timestamp=datetime.utcnow(),
            open=150.0,
            high=155.0,
            low=149.0,
            close=152.0,
            volume=1000000,
            source='test'
        )
        
        # Test serialization
        data_dict = data_point.to_dict()
        
        assert data_dict['symbol'] == 'AAPL'
        assert data_dict['open'] == 150.0
        assert data_dict['high'] == 155.0
        assert data_dict['low'] == 149.0
        assert data_dict['close'] == 152.0
        assert data_dict['volume'] == 1000000
        assert data_dict['source'] == 'test'
    
    @pytest.mark.asyncio
    async def test_pipeline_initialization(self):
        """Test pipeline initialization"""
        async with MarketDataPipeline() as pipeline:
            assert pipeline.validator is not None
            assert pipeline.storage is not None
            assert pipeline.polygon_client is not None
            assert pipeline.alpha_vantage_client is not None
    
    @pytest.mark.asyncio
    @patch('src.market_data_pipeline.PolygonDataClient.get_daily_bars')
    async def test_symbol_data_ingestion(self, mock_get_bars):
        """Test data ingestion for a single symbol"""
        # Mock successful data retrieval
        mock_data = [
            MarketDataPoint(
                symbol='AAPL',
                timestamp=datetime.utcnow(),
                open=150.0,
                high=155.0,
                low=149.0,
                close=152.0,
                volume=1000000,
                source='polygon'
            )
        ]
        mock_get_bars.return_value = mock_data
        
        async with MarketDataPipeline() as pipeline:
            result = await pipeline.ingest_symbol_data('AAPL', days_back=1)
            
            assert result.symbol == 'AAPL'
            assert result.data_type == 'market_data'
            assert result.record_count >= 0
    
    @pytest.mark.asyncio
    async def test_pipeline_status(self):
        """Test pipeline status reporting"""
        async with MarketDataPipeline() as pipeline:
            status = await pipeline.get_pipeline_status()
            
            assert 'timestamp' in status
            assert 'data_quality' in status
            assert 'rate_limits' in status

class TestIntegration:
    """Integration tests for complete workflows"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_data_flow(self):
        """Test complete data flow from ingestion to validation"""
        # This test requires actual API keys and network access
        # Skip if running in CI/CD environment
        if os.getenv('CI'):
            pytest.skip("Skipping integration test in CI environment")
        
        # Test with a small batch of symbols
        test_symbols = ['AAPL', 'MSFT']
        
        async with MarketDataPipeline() as pipeline:
            results = await pipeline.ingest_batch_symbols(test_symbols, max_concurrent=2)
            
            assert len(results) <= len(test_symbols)  # Some may fail due to rate limits
            
            # Check that at least one result is valid
            valid_results = [r for r in results if r.passed]
            if valid_results:
                assert len(valid_results) > 0
                
                # Verify data quality
                for result in valid_results:
                    assert result.quality_score >= 0.0
                    assert result.data_type == 'market_data'
    
    @pytest.mark.asyncio
    async def test_performance_requirements(self):
        """Test that performance requirements are met"""
        start_time = datetime.utcnow()
        
        # Test data validation performance
        validator = DataValidator()
        test_data = {
            'open': 150.0,
            'high': 155.0,
            'low': 149.0,
            'close': 152.0,
            'volume': 1000000,
            'timestamp': datetime.utcnow().timestamp()
        }
        
        # Validate 1000 records
        for i in range(1000):
            result = validator.validate_market_data(test_data, f'TEST{i}')
            assert result.quality_score >= 0.95
        
        end_time = datetime.utcnow()
        processing_time = (end_time - start_time).total_seconds()
        
        # Should process 1000 records in less than 10 seconds
        assert processing_time < 10.0
        
        # Calculate throughput
        throughput = 1000 / processing_time
        assert throughput >= 100  # At least 100 records per second

def test_configuration_validation():
    """Test configuration validation"""
    # Test API configuration
    assert config.apis['polygon'].api_key is not None
    assert config.apis['alpha_vantage'].api_key is not None
    
    # Test budget configuration
    assert config.budget['total_annual_budget'] == 100.0
    assert config.budget['monthly_budget'] == 8.33
    
    # Test hardware configuration
    assert config.hardware.max_concurrent_requests == 8
    assert config.hardware.gpu_acceleration is True

def test_api_key_validation():
    """Test API key validation"""
    validation_results = config.validate_api_keys()
    
    # Should have results for all APIs
    assert 'polygon' in validation_results
    assert 'alpha_vantage' in validation_results
    assert 'sec' in validation_results
    
    # Polygon and Alpha Vantage should be valid
    assert validation_results['polygon'] is True
    assert validation_results['alpha_vantage'] is True
    assert validation_results['sec'] is True  # SEC doesn't require key

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
