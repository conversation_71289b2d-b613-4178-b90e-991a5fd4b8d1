"""
QuantumEdge Financial Intelligence Tool - Production API Gateway
FastAPI-based REST API with real-time data endpoints and portfolio management
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

from src.config import config
from src.market_data_pipeline import MarketDataPipeline
from src.sentiment_engine import sentiment_engine
from src.risk_analytics import risk_analyzer
from src.alternative_data import alternative_data_engine
from src.monitoring_dashboard import data_quality_monitor
from src.rate_limiter import rate_limiter

logger = logging.getLogger('quantumedge.api')

# Pydantic models for API requests/responses
class SymbolRequest(BaseModel):
    symbol: str = Field(..., description="Stock symbol (e.g., AAPL)")
    days_back: Optional[int] = Field(30, description="Number of days of historical data")

class PortfolioRequest(BaseModel):
    symbols: List[str] = Field(..., description="List of stock symbols")
    weights: Optional[List[float]] = Field(None, description="Portfolio weights (equal if not provided)")

class RiskAnalysisResponse(BaseModel):
    symbol: str
    timestamp: str
    var_95: float
    var_99: float
    cvar_95: float
    cvar_99: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float

class SentimentResponse(BaseModel):
    symbol: str
    timestamp: str
    overall_sentiment: Dict[str, Any]
    news_sentiment: List[Dict[str, Any]]
    social_sentiment: List[Dict[str, Any]]

class SystemHealthResponse(BaseModel):
    timestamp: str
    status: str
    api_performance: Dict[str, Any]
    data_quality: Dict[str, Any]
    active_alerts: int

# Initialize FastAPI app
app = FastAPI(
    title="QuantumEdge Financial Intelligence API",
    description="Advanced financial risk analytics and portfolio intelligence platform",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global state
pipeline = None
initialized = False

@app.on_event("startup")
async def startup_event():
    """Initialize all systems on startup"""
    global pipeline, initialized
    
    try:
        logger.info("Initializing QuantumEdge API Gateway...")
        
        # Initialize rate limiter
        await rate_limiter.initialize()
        
        # Initialize sentiment engine
        await sentiment_engine.initialize()
        
        # Initialize market data pipeline
        pipeline = MarketDataPipeline()
        await pipeline.__aenter__()
        
        initialized = True
        logger.info("QuantumEdge API Gateway initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize API Gateway: {e}")
        initialized = False

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global pipeline
    
    if pipeline:
        await pipeline.__aexit__(None, None, None)
    
    logger.info("QuantumEdge API Gateway shutdown complete")

def check_initialization():
    """Dependency to check if system is initialized"""
    if not initialized:
        raise HTTPException(status_code=503, detail="System not initialized")
    return True

# Health check endpoints
@app.get("/health", response_model=Dict[str, str])
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy" if initialized else "initializing",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }

@app.get("/health/detailed", response_model=SystemHealthResponse)
async def detailed_health_check(init_check: bool = Depends(check_initialization)):
    """Detailed system health check"""
    try:
        health_data = await data_quality_monitor.get_system_health()
        
        return SystemHealthResponse(
            timestamp=health_data.get('timestamp', datetime.utcnow().isoformat()),
            status="healthy",
            api_performance=health_data.get('api_performance', {}),
            data_quality=health_data.get('metrics', {}),
            active_alerts=health_data.get('active_alerts', 0)
        )
        
    except Exception as e:
        logger.error(f"Error in detailed health check: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Market data endpoints
@app.post("/api/v1/market-data/ingest")
async def ingest_market_data(
    request: SymbolRequest,
    background_tasks: BackgroundTasks,
    init_check: bool = Depends(check_initialization)
):
    """Ingest market data for a symbol"""
    try:
        # Add to background tasks for async processing
        background_tasks.add_task(
            pipeline.ingest_symbol_data, 
            request.symbol, 
            request.days_back
        )
        
        return {
            "message": f"Market data ingestion started for {request.symbol}",
            "symbol": request.symbol,
            "days_back": request.days_back,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in market data ingestion: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/market-data/{symbol}")
async def get_market_data(
    symbol: str,
    limit: int = Query(100, description="Number of records to return"),
    init_check: bool = Depends(check_initialization)
):
    """Get historical market data for a symbol"""
    try:
        data = await pipeline.storage.get_latest_data(symbol, limit)
        
        return {
            "symbol": symbol,
            "data_points": len(data),
            "data": data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error retrieving market data for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Risk analytics endpoints
@app.post("/api/v1/risk/analyze", response_model=RiskAnalysisResponse)
async def analyze_risk(
    request: SymbolRequest,
    init_check: bool = Depends(check_initialization)
):
    """Perform comprehensive risk analysis for a symbol"""
    try:
        risk_metrics = await risk_analyzer.analyze_symbol_risk(
            request.symbol, 
            lookback_days=request.days_back or 252
        )
        
        return RiskAnalysisResponse(
            symbol=risk_metrics.symbol,
            timestamp=risk_metrics.timestamp.isoformat(),
            var_95=risk_metrics.var_95,
            var_99=risk_metrics.var_99,
            cvar_95=risk_metrics.cvar_95,
            cvar_99=risk_metrics.cvar_99,
            volatility=risk_metrics.volatility,
            sharpe_ratio=risk_metrics.sharpe_ratio,
            max_drawdown=risk_metrics.max_drawdown
        )
        
    except Exception as e:
        logger.error(f"Error in risk analysis for {request.symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/risk/portfolio")
async def analyze_portfolio_risk(
    request: PortfolioRequest,
    init_check: bool = Depends(check_initialization)
):
    """Perform portfolio-level risk analysis"""
    try:
        stress_test_results = await risk_analyzer.portfolio_stress_test(
            request.symbols,
            request.weights
        )
        
        return stress_test_results
        
    except Exception as e:
        logger.error(f"Error in portfolio risk analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/risk/regime")
async def detect_market_regime(
    symbol: str = Query("SPY", description="Market index symbol"),
    init_check: bool = Depends(check_initialization)
):
    """Detect current market regime"""
    try:
        regime_result = await risk_analyzer.detect_market_regime(symbol)
        
        return {
            "symbol": symbol,
            "regime_detection": regime_result.to_dict(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in regime detection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Sentiment analysis endpoints
@app.post("/api/v1/sentiment/analyze", response_model=SentimentResponse)
async def analyze_sentiment(
    request: SymbolRequest,
    init_check: bool = Depends(check_initialization)
):
    """Analyze sentiment for a symbol"""
    try:
        sentiment_data = await sentiment_engine.analyze_symbol_sentiment(request.symbol)
        
        return SentimentResponse(
            symbol=sentiment_data['symbol'],
            timestamp=sentiment_data['timestamp'],
            overall_sentiment=sentiment_data['overall_sentiment'],
            news_sentiment=sentiment_data['news_sentiment'],
            social_sentiment=sentiment_data['social_sentiment']
        )
        
    except Exception as e:
        logger.error(f"Error in sentiment analysis for {request.symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/sentiment/summary")
async def get_sentiment_summary(
    symbols: List[str],
    init_check: bool = Depends(check_initialization)
):
    """Get sentiment summary for multiple symbols"""
    try:
        summary = await sentiment_engine.get_sentiment_summary(symbols)
        return summary
        
    except Exception as e:
        logger.error(f"Error in sentiment summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Alternative data endpoints
@app.post("/api/v1/alternative-data/fundamentals")
async def analyze_fundamentals(
    request: SymbolRequest,
    init_check: bool = Depends(check_initialization)
):
    """Analyze company fundamentals using alternative data"""
    try:
        analysis = await alternative_data_engine.analyze_company_fundamentals(request.symbol)
        return analysis
        
    except Exception as e:
        logger.error(f"Error in fundamentals analysis for {request.symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/alternative-data/market-context")
async def get_market_context(init_check: bool = Depends(check_initialization)):
    """Get broader market context using economic indicators"""
    try:
        context = await alternative_data_engine.get_market_context()
        return context
        
    except Exception as e:
        logger.error(f"Error getting market context: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/alternative-data/report")
async def generate_alternative_data_report(
    symbols: List[str],
    init_check: bool = Depends(check_initialization)
):
    """Generate comprehensive alternative data report"""
    try:
        report = await alternative_data_engine.generate_alternative_data_report(symbols)
        return report
        
    except Exception as e:
        logger.error(f"Error generating alternative data report: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Monitoring endpoints
@app.get("/api/v1/monitoring/data-quality")
async def get_data_quality_report(
    hours_back: int = Query(24, description="Hours of historical data to analyze"),
    init_check: bool = Depends(check_initialization)
):
    """Get data quality report"""
    try:
        report = await data_quality_monitor.get_data_quality_report(hours_back)
        return report
        
    except Exception as e:
        logger.error(f"Error getting data quality report: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/monitoring/pipeline-status")
async def get_pipeline_status(init_check: bool = Depends(check_initialization)):
    """Get comprehensive pipeline status"""
    try:
        status = await pipeline.get_pipeline_status()
        return status
        
    except Exception as e:
        logger.error(f"Error getting pipeline status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Comprehensive analysis endpoint
@app.post("/api/v1/analyze/comprehensive")
async def comprehensive_analysis(
    request: SymbolRequest,
    background_tasks: BackgroundTasks,
    init_check: bool = Depends(check_initialization)
):
    """Perform comprehensive analysis including all data sources"""
    try:
        symbol = request.symbol
        
        # Start all analyses in parallel
        risk_task = risk_analyzer.analyze_symbol_risk(symbol)
        sentiment_task = sentiment_engine.analyze_symbol_sentiment(symbol)
        fundamentals_task = alternative_data_engine.analyze_company_fundamentals(symbol)
        
        # Wait for all to complete
        risk_result, sentiment_result, fundamentals_result = await asyncio.gather(
            risk_task, sentiment_task, fundamentals_task, return_exceptions=True
        )
        
        # Compile comprehensive report
        report = {
            "symbol": symbol,
            "timestamp": datetime.utcnow().isoformat(),
            "risk_analysis": risk_result.to_dict() if hasattr(risk_result, 'to_dict') else str(risk_result),
            "sentiment_analysis": sentiment_result if isinstance(sentiment_result, dict) else str(sentiment_result),
            "fundamentals_analysis": fundamentals_result if isinstance(fundamentals_result, dict) else str(fundamentals_result)
        }
        
        return report
        
    except Exception as e:
        logger.error(f"Error in comprehensive analysis for {request.symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "timestamp": datetime.utcnow().isoformat(),
            "path": str(request.url)
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """General exception handler"""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "timestamp": datetime.utcnow().isoformat(),
            "path": str(request.url)
        }
    )

def run_api_server(host: str = "0.0.0.0", port: int = 8000, reload: bool = False):
    """Run the API server"""
    uvicorn.run(
        "src.api_gateway:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )

if __name__ == "__main__":
    run_api_server(reload=True)
