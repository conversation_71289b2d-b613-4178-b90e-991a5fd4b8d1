"""
QuantumEdge Financial Intelligence Tool - Alpaca Paper Trading Integration
Live paper trading with Alpaca API for controlled deployment and backtesting
"""

import asyncio
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import pandas as pd
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Alpaca SDK imports
from alpaca.trading.client import TradingClient
from alpaca.trading.requests import (
    MarketOrderRequest, LimitOrderRequest, StopOrderRequest,
    GetOrdersRequest, GetAssetsRequest
)
from alpaca.trading.enums import (
    OrderSide, TimeInForce, OrderStatus, AssetClass, AssetStatus
)
from alpaca.data.historical import StockHistoricalDataClient
from alpaca.data.requests import StockBarsRequest, StockLatestQuoteRequest
from alpaca.data.timeframe import TimeFrame

from src.config import config

logger = logging.getLogger('quantumedge.alpaca_trading')

@dataclass
class TradingSignal:
    """Trading signal from QuantumEdge analysis"""
    symbol: str
    action: str  # 'BUY' or 'SELL'
    confidence: float
    sentiment_score: float
    risk_score: float
    regime_confidence: float
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    position_size: Optional[float] = None
    
    def to_dict(self) -> Dict:
        return {
            'symbol': self.symbol,
            'action': self.action,
            'confidence': self.confidence,
            'sentiment_score': self.sentiment_score,
            'risk_score': self.risk_score,
            'regime_confidence': self.regime_confidence,
            'target_price': self.target_price,
            'stop_loss': self.stop_loss,
            'position_size': self.position_size
        }

@dataclass
class TradeExecution:
    """Trade execution result"""
    signal: TradingSignal
    order_id: Optional[str]
    execution_price: Optional[float]
    execution_time: datetime
    status: str
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict:
        return {
            'signal': self.signal.to_dict(),
            'order_id': self.order_id,
            'execution_price': self.execution_price,
            'execution_time': self.execution_time.isoformat(),
            'status': self.status,
            'error_message': self.error_message
        }

class AlpacaTradingClient:
    """
    Alpaca paper trading client for QuantumEdge system
    Handles live paper trading with risk management and performance tracking
    """
    
    def __init__(self):
        self.api_key = os.getenv('ALPACA_API_KEY')
        self.api_secret = os.getenv('ALPACA_API_SECRET')
        self.paper_trading = True  # Always use paper trading for safety
        
        # Initialize Alpaca clients
        self.trading_client = None
        self.data_client = None
        self.initialized = False
        
        # Trading parameters
        self.max_position_size = 500.0  # $500 max per position (5% of $10k)
        self.stop_loss_pct = 0.02  # 2% stop loss
        self.take_profit_pct = 0.04  # 4% take profit
        self.max_portfolio_risk = 0.10  # 10% max portfolio risk
        
        # Performance tracking
        self.trades_executed = []
        self.performance_metrics = {}
        
    async def initialize(self):
        """Initialize Alpaca trading clients"""
        try:
            if not self.api_key or not self.api_secret:
                raise ValueError("Alpaca API credentials not found in environment")

            # Initialize trading client (paper trading)
            self.trading_client = TradingClient(
                api_key=self.api_key,
                secret_key=self.api_secret,
                paper=True,  # Force paper trading
                url_override="https://paper-api.alpaca.markets"  # Paper trading endpoint
            )
            
            # Initialize data client
            self.data_client = StockHistoricalDataClient(
                api_key=self.api_key,
                secret_key=self.api_secret
            )
            
            # Verify connection
            account = self.trading_client.get_account()
            logger.info(f"Connected to Alpaca paper trading account: {account.account_number}")
            logger.info(f"Account equity: ${float(account.equity):,.2f}")
            logger.info(f"Buying power: ${float(account.buying_power):,.2f}")
            
            self.initialized = True
            
        except Exception as e:
            logger.error(f"Failed to initialize Alpaca trading client: {e}")
            self.initialized = False
            raise
    
    async def get_account_info(self) -> Dict[str, Any]:
        """Get current account information"""
        if not self.initialized:
            await self.initialize()
        
        try:
            account = self.trading_client.get_account()
            positions = self.trading_client.get_all_positions()
            
            return {
                'account_number': account.account_number,
                'equity': float(account.equity),
                'cash': float(account.cash),
                'buying_power': float(account.buying_power),
                'portfolio_value': float(account.portfolio_value),
                'day_trade_count': account.daytrade_count,
                'positions_count': len(positions),
                'positions': [
                    {
                        'symbol': pos.symbol,
                        'qty': float(pos.qty),
                        'market_value': float(pos.market_value),
                        'unrealized_pl': float(pos.unrealized_pl),
                        'unrealized_plpc': float(pos.unrealized_plpc)
                    }
                    for pos in positions
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {'error': str(e)}
    
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for a symbol"""
        try:
            request = StockLatestQuoteRequest(symbol_or_symbols=[symbol])
            quotes = self.data_client.get_stock_latest_quote(request)
            
            if symbol in quotes:
                quote = quotes[symbol]
                # Use mid price between bid and ask
                return (quote.bid_price + quote.ask_price) / 2
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            return None
    
    async def validate_trading_signal(self, signal: TradingSignal) -> Tuple[bool, str]:
        """
        Validate trading signal against risk management rules
        
        Returns:
            Tuple of (is_valid, reason)
        """
        # Check minimum confidence thresholds
        if signal.confidence < 0.7:
            return False, f"Confidence too low: {signal.confidence:.3f} < 0.7"
        
        if signal.sentiment_score < 0.6:
            return False, f"Sentiment too low: {signal.sentiment_score:.3f} < 0.6"
        
        if signal.risk_score > 0.05:
            return False, f"Risk too high: {signal.risk_score:.3f} > 0.05"
        
        if signal.regime_confidence < 0.7:
            return False, f"Regime confidence too low: {signal.regime_confidence:.3f} < 0.7"
        
        # Check account status
        account = self.trading_client.get_account()
        if account.trading_blocked:
            return False, "Account trading is blocked"
        
        # Check position limits
        positions = self.trading_client.get_all_positions()
        current_positions = len(positions)
        
        if current_positions >= 10:  # Max 10 positions
            return False, f"Too many positions: {current_positions} >= 10"
        
        # Check if we already have a position in this symbol
        existing_position = None
        for pos in positions:
            if pos.symbol == signal.symbol:
                existing_position = pos
                break
        
        if signal.action == 'BUY' and existing_position and float(existing_position.qty) > 0:
            return False, f"Already have long position in {signal.symbol}"
        
        if signal.action == 'SELL' and existing_position and float(existing_position.qty) < 0:
            return False, f"Already have short position in {signal.symbol}"
        
        # Check buying power
        current_price = await self.get_current_price(signal.symbol)
        if not current_price:
            return False, f"Cannot get current price for {signal.symbol}"
        
        position_value = signal.position_size or self.max_position_size
        required_buying_power = position_value * 1.1  # 10% buffer
        
        if float(account.buying_power) < required_buying_power:
            return False, f"Insufficient buying power: ${float(account.buying_power):,.2f} < ${required_buying_power:,.2f}"
        
        return True, "Signal validated"
    
    async def execute_trade(self, signal: TradingSignal) -> TradeExecution:
        """
        Execute a trading signal with proper risk management
        
        Args:
            signal: TradingSignal to execute
            
        Returns:
            TradeExecution result
        """
        execution_time = datetime.utcnow()
        
        try:
            # Validate signal
            is_valid, reason = await self.validate_trading_signal(signal)
            if not is_valid:
                return TradeExecution(
                    signal=signal,
                    order_id=None,
                    execution_price=None,
                    execution_time=execution_time,
                    status='REJECTED',
                    error_message=reason
                )
            
            # Get current price
            current_price = await self.get_current_price(signal.symbol)
            if not current_price:
                return TradeExecution(
                    signal=signal,
                    order_id=None,
                    execution_price=None,
                    execution_time=execution_time,
                    status='FAILED',
                    error_message=f"Cannot get current price for {signal.symbol}"
                )
            
            # Calculate position size
            position_value = signal.position_size or self.max_position_size
            qty = position_value / current_price
            
            # Round to appropriate decimal places
            if current_price > 100:
                qty = round(qty, 2)  # 2 decimal places for expensive stocks
            else:
                qty = round(qty, 3)  # 3 decimal places for cheaper stocks
            
            # Create order request
            order_side = OrderSide.BUY if signal.action == 'BUY' else OrderSide.SELL
            
            # Use market order for immediate execution
            order_request = MarketOrderRequest(
                symbol=signal.symbol,
                qty=qty,
                side=order_side,
                time_in_force=TimeInForce.DAY
            )
            
            # Submit order
            order = self.trading_client.submit_order(order_request)
            
            logger.info(f"Order submitted: {signal.action} {qty} shares of {signal.symbol}")
            logger.info(f"Order ID: {order.id}")
            
            # Store trade execution
            execution = TradeExecution(
                signal=signal,
                order_id=order.id,
                execution_price=current_price,  # Approximate, will be updated when filled
                execution_time=execution_time,
                status='SUBMITTED'
            )
            
            self.trades_executed.append(execution)
            
            return execution
            
        except Exception as e:
            logger.error(f"Error executing trade for {signal.symbol}: {e}")
            return TradeExecution(
                signal=signal,
                order_id=None,
                execution_price=None,
                execution_time=execution_time,
                status='FAILED',
                error_message=str(e)
            )
    
    async def get_order_status(self, order_id: str) -> Dict[str, Any]:
        """Get status of a specific order"""
        try:
            order = self.trading_client.get_order_by_id(order_id)
            
            return {
                'order_id': order.id,
                'symbol': order.symbol,
                'qty': float(order.qty),
                'filled_qty': float(order.filled_qty or 0),
                'status': order.status.value,
                'side': order.side.value,
                'order_type': order.order_type.value,
                'submitted_at': order.submitted_at.isoformat() if order.submitted_at else None,
                'filled_at': order.filled_at.isoformat() if order.filled_at else None,
                'filled_avg_price': float(order.filled_avg_price) if order.filled_avg_price else None
            }
            
        except Exception as e:
            logger.error(f"Error getting order status for {order_id}: {e}")
            return {'error': str(e)}
    
    async def get_portfolio_performance(self) -> Dict[str, Any]:
        """Calculate portfolio performance metrics"""
        try:
            account = self.trading_client.get_account()
            positions = self.trading_client.get_all_positions()
            
            # Calculate basic metrics
            total_equity = float(account.equity)
            total_pl = sum(float(pos.unrealized_pl) for pos in positions)
            total_pl_pct = (total_pl / total_equity) * 100 if total_equity > 0 else 0
            
            # Calculate position metrics
            winning_positions = len([pos for pos in positions if float(pos.unrealized_pl) > 0])
            losing_positions = len([pos for pos in positions if float(pos.unrealized_pl) < 0])
            total_positions = len(positions)
            
            win_rate = (winning_positions / total_positions * 100) if total_positions > 0 else 0
            
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'total_equity': total_equity,
                'total_pl': total_pl,
                'total_pl_pct': total_pl_pct,
                'total_positions': total_positions,
                'winning_positions': winning_positions,
                'losing_positions': losing_positions,
                'win_rate': win_rate,
                'trades_executed': len(self.trades_executed),
                'account_status': {
                    'buying_power': float(account.buying_power),
                    'cash': float(account.cash),
                    'portfolio_value': float(account.portfolio_value)
                }
            }
            
        except Exception as e:
            logger.error(f"Error calculating portfolio performance: {e}")
            return {'error': str(e)}
    
    async def close_position(self, symbol: str, reason: str = "Manual close") -> Dict[str, Any]:
        """Close a position in a specific symbol"""
        try:
            # Get current position
            positions = self.trading_client.get_all_positions()
            position = None
            
            for pos in positions:
                if pos.symbol == symbol:
                    position = pos
                    break
            
            if not position:
                return {'error': f'No position found for {symbol}'}
            
            # Close the position
            close_result = self.trading_client.close_position(symbol)
            
            logger.info(f"Position closed for {symbol}: {reason}")
            
            return {
                'symbol': symbol,
                'qty_closed': float(position.qty),
                'market_value': float(position.market_value),
                'unrealized_pl': float(position.unrealized_pl),
                'reason': reason,
                'close_order_id': close_result.id if hasattr(close_result, 'id') else None
            }
            
        except Exception as e:
            logger.error(f"Error closing position for {symbol}: {e}")
            return {'error': str(e)}
    
    async def get_trading_history(self, days_back: int = 7) -> List[Dict[str, Any]]:
        """Get recent trading history"""
        try:
            # Get orders from the last N days
            start_date = datetime.utcnow() - timedelta(days=days_back)
            
            request = GetOrdersRequest(
                status=OrderStatus.ALL,
                limit=100
            )
            
            orders = self.trading_client.get_orders(request)
            
            history = []
            for order in orders:
                if order.submitted_at and order.submitted_at >= start_date:
                    history.append({
                        'order_id': order.id,
                        'symbol': order.symbol,
                        'side': order.side.value,
                        'qty': float(order.qty),
                        'filled_qty': float(order.filled_qty or 0),
                        'status': order.status.value,
                        'submitted_at': order.submitted_at.isoformat(),
                        'filled_at': order.filled_at.isoformat() if order.filled_at else None,
                        'filled_avg_price': float(order.filled_avg_price) if order.filled_avg_price else None
                    })
            
            return sorted(history, key=lambda x: x['submitted_at'], reverse=True)
            
        except Exception as e:
            logger.error(f"Error getting trading history: {e}")
            return []

# Global Alpaca trading client
alpaca_client = AlpacaTradingClient()

async def initialize_alpaca_trading():
    """Initialize Alpaca trading client"""
    await alpaca_client.initialize()
    logger.info("Alpaca paper trading client initialized")
