"""
QuantumEdge Financial Intelligence Tool - Market Data Pipeline
High-performance data ingestion with Polygon.io and Alpha Vantage
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import aiohttp
import polars as pl
import duckdb
from dataclasses import dataclass

from src.config import config
from src.rate_limiter import rate_limiter
from src.data_validation import DataValidator, ValidationResult

logger = logging.getLogger('quantumedge.market_data')

@dataclass
class MarketDataPoint:
    """Single market data point"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    adjusted_close: Optional[float] = None
    source: str = 'unknown'
    
    def to_dict(self) -> Dict:
        return {
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume,
            'adjusted_close': self.adjusted_close,
            'source': self.source
        }

class PolygonDataClient:
    """Polygon.io API client with rate limiting"""
    
    def __init__(self):
        self.base_url = config.apis['polygon'].base_url
        self.api_key = config.apis['polygon'].api_key
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_daily_bars(self, symbol: str, from_date: str, to_date: str) -> List[MarketDataPoint]:
        """Get daily OHLCV bars for a symbol"""
        if not self.api_key:
            raise ValueError("Polygon API key not configured")
        
        # Check rate limits
        can_proceed, rate_info = await rate_limiter.acquire('polygon')
        if not can_proceed:
            wait_time = await rate_limiter.wait_for_reset('polygon', rate_info.limit_type)
            logger.info(f"Rate limited, waiting {wait_time}s for Polygon API")
            await asyncio.sleep(wait_time)
            return await self.get_daily_bars(symbol, from_date, to_date)
        
        url = f"{self.base_url}/v2/aggs/ticker/{symbol}/range/1/day/{from_date}/{to_date}"
        params = {
            'apikey': self.api_key,
            'adjusted': 'true',
            'sort': 'asc'
        }
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_polygon_response(data, symbol)
                elif response.status == 429:
                    # Rate limited by server
                    logger.warning("Rate limited by Polygon API server")
                    await asyncio.sleep(12)  # Wait 12 seconds (5 requests per minute)
                    return await self.get_daily_bars(symbol, from_date, to_date)
                else:
                    logger.error(f"Polygon API error {response.status}: {await response.text()}")
                    return []
        except Exception as e:
            logger.error(f"Error fetching Polygon data for {symbol}: {e}")
            return []
    
    async def get_real_time_quote(self, symbol: str) -> Optional[Dict]:
        """Get real-time quote for a symbol"""
        if not self.api_key:
            raise ValueError("Polygon API key not configured")
        
        can_proceed, rate_info = await rate_limiter.acquire('polygon')
        if not can_proceed:
            return None
        
        url = f"{self.base_url}/v2/last/trade/{symbol}"
        params = {'apikey': self.api_key}
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('results', {})
                else:
                    logger.error(f"Polygon quote error {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error fetching Polygon quote for {symbol}: {e}")
            return None
    
    def _parse_polygon_response(self, data: Dict, symbol: str) -> List[MarketDataPoint]:
        """Parse Polygon API response into MarketDataPoint objects"""
        results = data.get('results', [])
        market_data = []
        
        for bar in results:
            try:
                market_data.append(MarketDataPoint(
                    symbol=symbol,
                    timestamp=datetime.fromtimestamp(bar['t'] / 1000),  # Polygon uses milliseconds
                    open=float(bar['o']),
                    high=float(bar['h']),
                    low=float(bar['l']),
                    close=float(bar['c']),
                    volume=int(bar['v']),
                    adjusted_close=float(bar.get('vw', bar['c'])),  # Volume weighted average price
                    source='polygon'
                ))
            except (KeyError, ValueError, TypeError) as e:
                logger.warning(f"Error parsing Polygon bar for {symbol}: {e}")
                continue
        
        return market_data

class AlphaVantageDataClient:
    """Alpha Vantage API client with rate limiting"""
    
    def __init__(self):
        self.base_url = config.apis['alpha_vantage'].base_url
        self.api_key = config.apis['alpha_vantage'].api_key
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_daily_data(self, symbol: str) -> List[MarketDataPoint]:
        """Get daily time series data from Alpha Vantage"""
        if not self.api_key:
            raise ValueError("Alpha Vantage API key not configured")
        
        can_proceed, rate_info = await rate_limiter.acquire('alpha_vantage')
        if not can_proceed:
            wait_time = await rate_limiter.wait_for_reset('alpha_vantage', rate_info.limit_type)
            logger.info(f"Rate limited, waiting {wait_time}s for Alpha Vantage API")
            await asyncio.sleep(wait_time)
            return await self.get_daily_data(symbol)
        
        url = f"{self.base_url}/query"
        params = {
            'function': 'TIME_SERIES_DAILY_ADJUSTED',
            'symbol': symbol,
            'apikey': self.api_key,
            'outputsize': 'compact'  # Last 100 data points
        }
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_alpha_vantage_response(data, symbol)
                else:
                    logger.error(f"Alpha Vantage API error {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Error fetching Alpha Vantage data for {symbol}: {e}")
            return []
    
    async def get_technical_indicators(self, symbol: str, indicator: str = 'RSI') -> Dict:
        """Get technical indicators from Alpha Vantage"""
        if not self.api_key:
            raise ValueError("Alpha Vantage API key not configured")
        
        can_proceed, rate_info = await rate_limiter.acquire('alpha_vantage')
        if not can_proceed:
            return {}
        
        url = f"{self.base_url}/query"
        params = {
            'function': indicator,
            'symbol': symbol,
            'interval': 'daily',
            'time_period': 14,
            'series_type': 'close',
            'apikey': self.api_key
        }
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"Alpha Vantage indicators error {response.status}")
                    return {}
        except Exception as e:
            logger.error(f"Error fetching Alpha Vantage indicators for {symbol}: {e}")
            return {}
    
    def _parse_alpha_vantage_response(self, data: Dict, symbol: str) -> List[MarketDataPoint]:
        """Parse Alpha Vantage response into MarketDataPoint objects"""
        time_series = data.get('Time Series (Daily Adjusted)', {})
        market_data = []
        
        for date_str, values in time_series.items():
            try:
                market_data.append(MarketDataPoint(
                    symbol=symbol,
                    timestamp=datetime.strptime(date_str, '%Y-%m-%d'),
                    open=float(values['1. open']),
                    high=float(values['2. high']),
                    low=float(values['3. low']),
                    close=float(values['4. close']),
                    volume=int(values['6. volume']),
                    adjusted_close=float(values['5. adjusted close']),
                    source='alpha_vantage'
                ))
            except (KeyError, ValueError, TypeError) as e:
                logger.warning(f"Error parsing Alpha Vantage data for {symbol} on {date_str}: {e}")
                continue
        
        return market_data

class MarketDataStorage:
    """High-performance storage for market data using DuckDB"""
    
    def __init__(self, db_path: str = "data/market_data.duckdb"):
        self.db_path = db_path
        self.connection = None
        self._initialize_schema()
    
    def _initialize_schema(self):
        """Initialize database schema"""
        try:
            conn = duckdb.connect(self.db_path)
            
            # Create market data table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS market_data (
                    symbol VARCHAR,
                    timestamp TIMESTAMP,
                    open DECIMAL(10,4),
                    high DECIMAL(10,4),
                    low DECIMAL(10,4),
                    close DECIMAL(10,4),
                    volume BIGINT,
                    adjusted_close DECIMAL(10,4),
                    source VARCHAR,
                    ingestion_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (symbol, timestamp, source)
                )
            """)
            
            # Create indexes for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_market_data_symbol ON market_data(symbol)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp)")
            
            # Create validation results table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS validation_results (
                    id UUID DEFAULT gen_random_uuid(),
                    data_type VARCHAR,
                    symbol VARCHAR,
                    timestamp TIMESTAMP,
                    quality_score DECIMAL(3,2),
                    passed BOOLEAN,
                    record_count INTEGER,
                    validation_details JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.close()
            logger.info("Market data storage schema initialized")
            
        except Exception as e:
            logger.error(f"Error initializing database schema: {e}")
    
    async def store_market_data(self, data_points: List[MarketDataPoint], 
                               validation_result: Optional[ValidationResult] = None):
        """Store market data points with validation results"""
        if not data_points:
            return
        
        try:
            conn = duckdb.connect(self.db_path)
            
            # Convert to DataFrame for bulk insert
            df_data = [point.to_dict() for point in data_points]
            df = pl.DataFrame(df_data)
            
            # Insert market data
            conn.execute("INSERT OR REPLACE INTO market_data SELECT * FROM df")
            
            # Store validation results if provided
            if validation_result:
                conn.execute("""
                    INSERT INTO validation_results 
                    (data_type, symbol, timestamp, quality_score, passed, record_count, validation_details)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, [
                    validation_result.data_type,
                    validation_result.symbol,
                    validation_result.timestamp,
                    validation_result.quality_score,
                    validation_result.passed,
                    validation_result.record_count,
                    str(validation_result.to_dict())
                ])
            
            conn.close()
            logger.info(f"Stored {len(data_points)} market data points")
            
        except Exception as e:
            logger.error(f"Error storing market data: {e}")
    
    async def get_latest_data(self, symbol: str, limit: int = 100) -> List[Dict]:
        """Get latest market data for a symbol"""
        try:
            conn = duckdb.connect(self.db_path)
            
            result = conn.execute("""
                SELECT * FROM market_data 
                WHERE symbol = ? 
                ORDER BY timestamp DESC 
                LIMIT ?
            """, [symbol, limit]).fetchall()
            
            columns = [desc[0] for desc in conn.description]
            conn.close()
            
            return [dict(zip(columns, row)) for row in result]
            
        except Exception as e:
            logger.error(f"Error retrieving market data for {symbol}: {e}")
            return []
    
    async def get_data_quality_stats(self) -> Dict:
        """Get data quality statistics"""
        try:
            conn = duckdb.connect(self.db_path)
            
            stats = conn.execute("""
                SELECT 
                    COUNT(*) as total_validations,
                    AVG(quality_score) as avg_quality_score,
                    SUM(CASE WHEN passed THEN 1 ELSE 0 END) as passed_validations,
                    COUNT(DISTINCT symbol) as symbols_validated
                FROM validation_results
                WHERE timestamp >= CURRENT_TIMESTAMP - INTERVAL '24 hours'
            """).fetchone()
            
            conn.close()
            
            return {
                'total_validations': stats[0] or 0,
                'avg_quality_score': float(stats[1] or 0),
                'passed_validations': stats[2] or 0,
                'symbols_validated': stats[3] or 0,
                'pass_rate': (stats[2] / stats[0]) if stats[0] > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting data quality stats: {e}")
            return {}

class MarketDataPipeline:
    """Main market data pipeline orchestrator"""
    
    def __init__(self):
        self.validator = DataValidator()
        self.storage = MarketDataStorage()
        self.polygon_client = None
        self.alpha_vantage_client = None
        
    async def __aenter__(self):
        self.polygon_client = PolygonDataClient()
        self.alpha_vantage_client = AlphaVantageDataClient()
        await self.polygon_client.__aenter__()
        await self.alpha_vantage_client.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.polygon_client:
            await self.polygon_client.__aexit__(exc_type, exc_val, exc_tb)
        if self.alpha_vantage_client:
            await self.alpha_vantage_client.__aexit__(exc_type, exc_val, exc_tb)
    
    async def ingest_symbol_data(self, symbol: str, days_back: int = 30) -> ValidationResult:
        """Ingest and validate data for a single symbol"""
        from_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        to_date = datetime.now().strftime('%Y-%m-%d')
        
        # Try Polygon first (primary source)
        data_points = await self.polygon_client.get_daily_bars(symbol, from_date, to_date)
        
        # Fallback to Alpha Vantage if Polygon fails
        if not data_points:
            logger.info(f"Polygon data unavailable for {symbol}, trying Alpha Vantage")
            data_points = await self.alpha_vantage_client.get_daily_data(symbol)
        
        if not data_points:
            logger.warning(f"No data available for {symbol}")
            return ValidationResult(
                data_type='market_data',
                symbol=symbol,
                timestamp=datetime.utcnow(),
                quality_score=0.0,
                checks=[],
                passed=False,
                record_count=0
            )
        
        # Validate the most recent data point
        latest_data = data_points[-1]
        validation_result = self.validator.validate_market_data(
            latest_data.to_dict(), symbol
        )
        
        # Store data and validation results
        await self.storage.store_market_data(data_points, validation_result)
        
        logger.info(f"Ingested {len(data_points)} data points for {symbol}, "
                   f"quality score: {validation_result.quality_score:.2f}")
        
        return validation_result
    
    async def ingest_batch_symbols(self, symbols: List[str], 
                                  max_concurrent: int = 5) -> List[ValidationResult]:
        """Ingest data for multiple symbols concurrently"""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def ingest_with_semaphore(symbol):
            async with semaphore:
                return await self.ingest_symbol_data(symbol)
        
        tasks = [ingest_with_semaphore(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and return valid results
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, ValidationResult):
                valid_results.append(result)
            else:
                logger.error(f"Error ingesting data for {symbols[i]}: {result}")
        
        return valid_results
    
    async def get_pipeline_status(self) -> Dict:
        """Get comprehensive pipeline status"""
        quality_stats = await self.storage.get_data_quality_stats()
        
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'data_quality': quality_stats,
            'rate_limits': {
                'polygon': await rate_limiter.get_usage_stats('polygon'),
                'alpha_vantage': await rate_limiter.get_usage_stats('alpha_vantage')
            }
        }
