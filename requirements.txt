# QuantumEdge Financial Intelligence Tool - Dependencies
# Optimized for Apple M1 Max with 64GB RAM and 32-core GPU

# Core Framework
fastapi==0.103.1
uvicorn[standard]==0.23.2
pydantic==2.4.2

# Data Processing (M1 Max Optimized)
polars==0.18.15
pandas==2.1.1
numpy==1.24.3

# Database & Storage
duckdb==0.8.1
sqlite3  # Built-in Python module
redis==4.6.0

# Machine Learning (PyTorch MPS for Apple Silicon)
torch==2.0.1
torchvision==0.15.2
torchaudio==0.13.1
scikit-learn==1.3.0

# Time Series & Financial Analysis
prophet==1.1.4
yfinance==0.2.18
ta-lib==0.4.26  # Technical analysis library

# HTTP & API Clients
aiohttp==3.8.5
httpx==0.24.1
requests==2.31.0

# Data Validation & Quality
pydantic==2.4.2
cerberus==1.3.4
great-expectations==0.17.12

# Caching & Rate Limiting
redis==4.6.0
aioredis==2.0.1
slowapi==0.1.9

# Web Interface
streamlit==1.25.0
plotly==5.15.0
dash==2.13.0

# Monitoring & Logging
prometheus-client==0.17.1
structlog==23.1.0
loguru==0.7.0

# Testing Framework
pytest==7.4.2
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.24.1  # For testing FastAPI

# Development Tools
black==23.7.0
isort==5.12.0
mypy==1.5.1
pre-commit==3.3.3

# Environment & Configuration
python-dotenv==1.0.0
pyyaml==6.0.1
click==8.1.7

# Financial Data Sources
alpha-vantage==2.3.1
polygon-api-client==1.12.3
sec-api==1.0.17

# Alternative Data
praw==7.7.1  # Reddit API
requests-oauthlib==1.3.1  # OAuth for APIs

# Satellite & Geospatial (Optional - for Phase 3)
# sentinelhub==3.9.0  # Uncomment when implementing satellite data
# opencv-python==4.8.0  # Computer vision for satellite imagery

# Compliance & Security
cryptography==41.0.4
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# Async & Concurrency
asyncio  # Built-in Python module
concurrent.futures  # Built-in Python module
aiofiles==23.2.1

# Data Export & Reporting
openpyxl==3.1.2
xlsxwriter==3.1.2
reportlab==4.0.4

# Utilities
python-dateutil==2.8.2
pytz==2023.3
tqdm==4.66.1
rich==13.5.2  # Beautiful terminal output
