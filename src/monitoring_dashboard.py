"""
QuantumEdge Financial Intelligence Tool - Data Quality & Monitoring Dashboard
Real-time monitoring and anomaly detection for all data sources
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import json
import duckdb
from dataclasses import dataclass, asdict
from collections import defaultdict
import statistics

from src.config import config
from src.data_validation import DataValidator, ValidationResult
from src.rate_limiter import rate_limiter

logger = logging.getLogger('quantumedge.monitoring')

@dataclass
class SystemMetrics:
    """System performance metrics"""
    timestamp: datetime
    api_response_times: Dict[str, float]
    data_quality_scores: Dict[str, float]
    validation_pass_rates: Dict[str, float]
    error_rates: Dict[str, float]
    throughput_metrics: Dict[str, float]
    memory_usage: float
    cpu_usage: float
    
    def to_dict(self) -> Dict:
        return {
            'timestamp': self.timestamp.isoformat(),
            'api_response_times': self.api_response_times,
            'data_quality_scores': self.data_quality_scores,
            'validation_pass_rates': self.validation_pass_rates,
            'error_rates': self.error_rates,
            'throughput_metrics': self.throughput_metrics,
            'memory_usage': self.memory_usage,
            'cpu_usage': self.cpu_usage
        }

@dataclass
class DataQualityAlert:
    """Data quality alert"""
    alert_id: str
    timestamp: datetime
    severity: str  # 'low', 'medium', 'high', 'critical'
    source: str
    symbol: Optional[str]
    metric: str
    current_value: float
    threshold: float
    message: str
    
    def to_dict(self) -> Dict:
        return asdict(self)

class AnomalyDetector:
    """Statistical anomaly detection for financial data"""
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.historical_data = defaultdict(list)
        
    def add_data_point(self, metric_name: str, value: float):
        """Add a new data point for anomaly detection"""
        self.historical_data[metric_name].append(value)
        
        # Keep only the last window_size points
        if len(self.historical_data[metric_name]) > self.window_size:
            self.historical_data[metric_name] = self.historical_data[metric_name][-self.window_size:]
    
    def detect_anomaly(self, metric_name: str, value: float, 
                      threshold_std: float = 2.0) -> Tuple[bool, float]:
        """
        Detect if a value is anomalous using statistical methods
        
        Returns:
            Tuple of (is_anomaly, z_score)
        """
        if metric_name not in self.historical_data:
            return False, 0.0
        
        data = self.historical_data[metric_name]
        if len(data) < 10:  # Need minimum data points
            return False, 0.0
        
        try:
            mean = statistics.mean(data)
            std_dev = statistics.stdev(data)
            
            if std_dev == 0:
                return False, 0.0
            
            z_score = abs(value - mean) / std_dev
            is_anomaly = z_score > threshold_std
            
            return is_anomaly, z_score
            
        except Exception as e:
            logger.warning(f"Error in anomaly detection for {metric_name}: {e}")
            return False, 0.0
    
    def get_statistics(self, metric_name: str) -> Dict[str, float]:
        """Get statistical summary for a metric"""
        if metric_name not in self.historical_data:
            return {}
        
        data = self.historical_data[metric_name]
        if len(data) < 2:
            return {}
        
        try:
            return {
                'mean': statistics.mean(data),
                'median': statistics.median(data),
                'std_dev': statistics.stdev(data),
                'min': min(data),
                'max': max(data),
                'count': len(data)
            }
        except Exception:
            return {}

class DataQualityMonitor:
    """Monitor data quality across all sources"""
    
    def __init__(self, db_path: str = "data/monitoring.duckdb"):
        self.db_path = db_path
        self.validator = DataValidator()
        self.anomaly_detector = AnomalyDetector()
        self.alerts = []
        self._initialize_schema()
        
    def _initialize_schema(self):
        """Initialize monitoring database schema"""
        try:
            conn = duckdb.connect(self.db_path)
            
            # System metrics table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS system_metrics (
                    timestamp TIMESTAMP,
                    metric_name VARCHAR,
                    metric_value DECIMAL(10,4),
                    source VARCHAR,
                    symbol VARCHAR,
                    metadata JSON
                )
            """)
            
            # Data quality alerts table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS quality_alerts (
                    alert_id VARCHAR PRIMARY KEY,
                    timestamp TIMESTAMP,
                    severity VARCHAR,
                    source VARCHAR,
                    symbol VARCHAR,
                    metric VARCHAR,
                    current_value DECIMAL(10,4),
                    threshold DECIMAL(10,4),
                    message TEXT,
                    resolved BOOLEAN DEFAULT FALSE
                )
            """)
            
            # API performance table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS api_performance (
                    timestamp TIMESTAMP,
                    api_name VARCHAR,
                    endpoint VARCHAR,
                    response_time DECIMAL(8,3),
                    status_code INTEGER,
                    success BOOLEAN,
                    error_message TEXT
                )
            """)
            
            conn.close()
            logger.info("Monitoring database schema initialized")
            
        except Exception as e:
            logger.error(f"Error initializing monitoring schema: {e}")
    
    async def record_api_performance(self, api_name: str, endpoint: str, 
                                   response_time: float, status_code: int, 
                                   success: bool, error_message: Optional[str] = None):
        """Record API performance metrics"""
        try:
            conn = duckdb.connect(self.db_path)
            
            conn.execute("""
                INSERT INTO api_performance 
                (timestamp, api_name, endpoint, response_time, status_code, success, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, [
                datetime.utcnow(),
                api_name,
                endpoint,
                response_time,
                status_code,
                success,
                error_message
            ])
            
            conn.close()
            
            # Check for anomalies
            metric_name = f"api_response_time_{api_name}"
            self.anomaly_detector.add_data_point(metric_name, response_time)
            
            is_anomaly, z_score = self.anomaly_detector.detect_anomaly(metric_name, response_time)
            if is_anomaly:
                await self._create_alert(
                    severity='medium',
                    source=api_name,
                    metric='response_time',
                    current_value=response_time,
                    threshold=2.0,
                    message=f"Unusual response time for {api_name}: {response_time:.3f}s (z-score: {z_score:.2f})"
                )
            
        except Exception as e:
            logger.error(f"Error recording API performance: {e}")
    
    async def record_data_quality(self, validation_result: ValidationResult):
        """Record data quality metrics"""
        try:
            conn = duckdb.connect(self.db_path)
            
            # Record overall quality score
            conn.execute("""
                INSERT INTO system_metrics 
                (timestamp, metric_name, metric_value, source, symbol, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            """, [
                validation_result.timestamp,
                'data_quality_score',
                validation_result.quality_score,
                validation_result.data_type,
                validation_result.symbol,
                json.dumps(validation_result.to_dict())
            ])
            
            # Record individual check scores
            for check in validation_result.checks:
                conn.execute("""
                    INSERT INTO system_metrics 
                    (timestamp, metric_name, metric_value, source, symbol, metadata)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, [
                    validation_result.timestamp,
                    f"check_{check.check_name}",
                    check.score,
                    validation_result.data_type,
                    validation_result.symbol,
                    json.dumps({'severity': check.severity.value, 'message': check.message})
                ])
            
            conn.close()
            
            # Check for quality degradation
            if validation_result.quality_score < 0.8:
                await self._create_alert(
                    severity='high' if validation_result.quality_score < 0.5 else 'medium',
                    source=validation_result.data_type,
                    symbol=validation_result.symbol,
                    metric='data_quality',
                    current_value=validation_result.quality_score,
                    threshold=0.8,
                    message=f"Data quality degradation for {validation_result.symbol}: {validation_result.quality_score:.2f}"
                )
            
        except Exception as e:
            logger.error(f"Error recording data quality: {e}")
    
    async def _create_alert(self, severity: str, source: str, metric: str,
                          current_value: float, threshold: float, message: str,
                          symbol: Optional[str] = None):
        """Create a data quality alert"""
        alert = DataQualityAlert(
            alert_id=f"{source}_{metric}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            timestamp=datetime.utcnow(),
            severity=severity,
            source=source,
            symbol=symbol,
            metric=metric,
            current_value=current_value,
            threshold=threshold,
            message=message
        )
        
        self.alerts.append(alert)
        
        # Store in database
        try:
            conn = duckdb.connect(self.db_path)
            
            conn.execute("""
                INSERT INTO quality_alerts 
                (alert_id, timestamp, severity, source, symbol, metric, 
                 current_value, threshold, message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, [
                alert.alert_id,
                alert.timestamp,
                alert.severity,
                alert.source,
                alert.symbol,
                alert.metric,
                alert.current_value,
                alert.threshold,
                alert.message
            ])
            
            conn.close()
            
            # Log alert
            logger.warning(f"Data Quality Alert [{severity.upper()}]: {message}")
            
        except Exception as e:
            logger.error(f"Error storing alert: {e}")
    
    async def get_system_health(self) -> Dict[str, Any]:
        """Get comprehensive system health metrics"""
        try:
            conn = duckdb.connect(self.db_path)
            
            # Get recent metrics (last 24 hours)
            recent_metrics = conn.execute("""
                SELECT 
                    metric_name,
                    AVG(metric_value) as avg_value,
                    MIN(metric_value) as min_value,
                    MAX(metric_value) as max_value,
                    COUNT(*) as count
                FROM system_metrics 
                WHERE timestamp >= ?
                GROUP BY metric_name
            """, [datetime.utcnow() - timedelta(hours=24)]).fetchall()
            
            # Get API performance
            api_performance = conn.execute("""
                SELECT 
                    api_name,
                    AVG(response_time) as avg_response_time,
                    SUM(CASE WHEN success THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as success_rate,
                    COUNT(*) as total_requests
                FROM api_performance 
                WHERE timestamp >= ?
                GROUP BY api_name
            """, [datetime.utcnow() - timedelta(hours=24)]).fetchall()
            
            # Get recent alerts
            recent_alerts = conn.execute("""
                SELECT * FROM quality_alerts 
                WHERE timestamp >= ? AND resolved = FALSE
                ORDER BY timestamp DESC
                LIMIT 10
            """, [datetime.utcnow() - timedelta(hours=24)]).fetchall()
            
            conn.close()
            
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'metrics': {
                    row[0]: {
                        'avg': float(row[1]),
                        'min': float(row[2]),
                        'max': float(row[3]),
                        'count': row[4]
                    }
                    for row in recent_metrics
                },
                'api_performance': {
                    row[0]: {
                        'avg_response_time': float(row[1]),
                        'success_rate': float(row[2]),
                        'total_requests': row[3]
                    }
                    for row in api_performance
                },
                'active_alerts': len(recent_alerts),
                'alert_summary': {
                    'critical': len([a for a in recent_alerts if a[3] == 'critical']),
                    'high': len([a for a in recent_alerts if a[3] == 'high']),
                    'medium': len([a for a in recent_alerts if a[3] == 'medium']),
                    'low': len([a for a in recent_alerts if a[3] == 'low'])
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {'error': str(e)}
    
    async def get_data_quality_report(self, hours_back: int = 24) -> Dict[str, Any]:
        """Generate comprehensive data quality report"""
        try:
            conn = duckdb.connect(self.db_path)
            
            # Overall quality trends
            quality_trends = conn.execute("""
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour,
                    AVG(metric_value) as avg_quality,
                    COUNT(*) as validations
                FROM system_metrics 
                WHERE metric_name = 'data_quality_score' 
                AND timestamp >= ?
                GROUP BY DATE_TRUNC('hour', timestamp)
                ORDER BY hour
            """, [datetime.utcnow() - timedelta(hours=hours_back)]).fetchall()
            
            # Quality by source
            quality_by_source = conn.execute("""
                SELECT 
                    source,
                    AVG(metric_value) as avg_quality,
                    MIN(metric_value) as min_quality,
                    MAX(metric_value) as max_quality,
                    COUNT(*) as validations
                FROM system_metrics 
                WHERE metric_name = 'data_quality_score' 
                AND timestamp >= ?
                GROUP BY source
            """, [datetime.utcnow() - timedelta(hours=hours_back)]).fetchall()
            
            # Failed validations
            failed_validations = conn.execute("""
                SELECT 
                    source,
                    symbol,
                    metric_value,
                    timestamp
                FROM system_metrics 
                WHERE metric_name = 'data_quality_score' 
                AND metric_value < 0.8
                AND timestamp >= ?
                ORDER BY timestamp DESC
                LIMIT 20
            """, [datetime.utcnow() - timedelta(hours=hours_back)]).fetchall()
            
            conn.close()
            
            return {
                'report_timestamp': datetime.utcnow().isoformat(),
                'period_hours': hours_back,
                'quality_trends': [
                    {
                        'hour': row[0].isoformat(),
                        'avg_quality': float(row[1]),
                        'validations': row[2]
                    }
                    for row in quality_trends
                ],
                'quality_by_source': {
                    row[0]: {
                        'avg_quality': float(row[1]),
                        'min_quality': float(row[2]),
                        'max_quality': float(row[3]),
                        'validations': row[4]
                    }
                    for row in quality_by_source
                },
                'failed_validations': [
                    {
                        'source': row[0],
                        'symbol': row[1],
                        'quality_score': float(row[2]),
                        'timestamp': row[3].isoformat()
                    }
                    for row in failed_validations
                ]
            }
            
        except Exception as e:
            logger.error(f"Error generating quality report: {e}")
            return {'error': str(e)}

# Global monitoring instance
data_quality_monitor = DataQualityMonitor()

async def initialize_monitoring():
    """Initialize the monitoring system"""
    logger.info("Data quality monitoring system initialized")
