"""
QuantumEdge Financial Intelligence Tool - Automated Daily Simulation System
Runs pre-market analysis, executes trades, and monitors performance automatically
"""

import asyncio
import logging
import json
import os
import smtplib
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Any
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import pandas as pd
import numpy as np
import schedule

from src.simulation_trading_client import simulation_client
from src.real_backtesting_engine import backtest_engine
from src.alpaca_trading_client import TradingSignal

logger = logging.getLogger('quantumedge.automated_daily')

class AutomatedDailySystem:
    """
    Automated daily trading system that runs pre-market analysis,
    executes trades, and monitors performance with alerts
    """
    
    def __init__(self):
        self.watchlist = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
        self.system_active = False
        self.daily_results = []
        self.performance_history = []
        
        # Performance thresholds for alerts
        self.min_win_rate = 60.0  # 60%
        self.min_sharpe_ratio = 1.0
        self.max_drawdown = 10.0  # 10%
        self.min_daily_return = -2.0  # -2% daily loss threshold
        
        # Email configuration (optional)
        self.email_alerts_enabled = False
        self.smtp_server = "smtp.gmail.com"
        self.smtp_port = 587
        self.email_user = os.getenv('EMAIL_USER')
        self.email_password = os.getenv('EMAIL_PASSWORD')
        self.alert_recipients = os.getenv('ALERT_RECIPIENTS', '').split(',')
        
        # Performance tracking
        self.rolling_30day_performance = []
        self.system_start_date = datetime.utcnow().date()
        
    async def initialize(self):
        """Initialize the automated daily system"""
        try:
            logger.info("🚀 Initializing Automated Daily System")
            
            # Initialize simulation client
            await simulation_client.initialize()
            
            # Load historical performance if exists
            await self._load_performance_history()
            
            self.system_active = True
            logger.info("✅ Automated Daily System initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize automated system: {e}")
            raise
    
    async def run_pre_market_analysis(self) -> Dict[str, Any]:
        """
        Run comprehensive pre-market analysis using QuantumEdge methodology
        """
        logger.info("📊 Starting Pre-Market Analysis")
        analysis_start = datetime.utcnow()
        
        analysis_results = {
            'timestamp': analysis_start.isoformat(),
            'symbols_analyzed': self.watchlist,
            'symbol_analysis': {},
            'trading_signals': [],
            'market_context': {},
            'system_health': {}
        }
        
        try:
            # Analyze each symbol using enhanced QuantumEdge methodology
            for symbol in self.watchlist:
                logger.info(f"📈 Analyzing {symbol}...")
                
                # Get recent market data (30 days)
                end_date = datetime.utcnow()
                start_date = end_date - timedelta(days=30)
                
                # Use yfinance for real-time data
                import yfinance as yf
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period="30d", interval="1d")
                
                if hist.empty:
                    logger.warning(f"⚠️ No data available for {symbol}")
                    continue
                
                # Convert to our format
                data = []
                for date, row in hist.iterrows():
                    data.append({
                        'timestamp': date,
                        'open': float(row['Open']),
                        'high': float(row['High']),
                        'low': float(row['Low']),
                        'close': float(row['Close']),
                        'volume': int(row['Volume'])
                    })
                
                df = pd.DataFrame(data)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                
                # Generate trading signal using enhanced analysis
                signal = await self._generate_enhanced_signal(symbol, df)
                
                # Store analysis results
                symbol_analysis = {
                    'symbol': symbol,
                    'data_points': len(df),
                    'current_price': df['close'].iloc[-1],
                    'signal_generated': signal is not None,
                    'analysis_timestamp': datetime.utcnow().isoformat()
                }
                
                if signal:
                    symbol_analysis['signal'] = signal.to_dict()
                    analysis_results['trading_signals'].append(signal.to_dict())
                
                analysis_results['symbol_analysis'][symbol] = symbol_analysis
            
            # Market context analysis
            analysis_results['market_context'] = await self._analyze_market_context()
            
            # System health check
            analysis_results['system_health'] = await self._check_system_health()
            
            analysis_duration = (datetime.utcnow() - analysis_start).total_seconds()
            logger.info(f"✅ Pre-market analysis completed in {analysis_duration:.2f} seconds")
            logger.info(f"📊 Generated {len(analysis_results['trading_signals'])} trading signals")
            
            return analysis_results
            
        except Exception as e:
            logger.error(f"❌ Error in pre-market analysis: {e}")
            analysis_results['error'] = str(e)
            return analysis_results
    
    async def _generate_enhanced_signal(self, symbol: str, data: pd.DataFrame) -> Optional[TradingSignal]:
        """Generate enhanced trading signal using QuantumEdge methodology"""
        try:
            if len(data) < 20:
                return None
            
            # Calculate returns
            data['returns'] = data['close'].pct_change()
            returns = data['returns'].dropna().values
            
            if len(returns) < 10:
                return None
            
            # Enhanced risk analysis
            var_95 = np.percentile(returns, 5)
            cvar_95 = np.mean(returns[returns <= var_95]) if len(returns[returns <= var_95]) > 0 else var_95
            risk_score = abs(cvar_95)
            
            # Enhanced sentiment analysis
            recent_returns = returns[-10:]
            momentum = np.mean(recent_returns)
            
            # Volume analysis
            volumes = data['volume'].tail(10).values
            avg_volume = np.mean(volumes)
            recent_volume = volumes[-1]
            volume_factor = min(2.0, recent_volume / avg_volume) if avg_volume > 0 else 1.0
            
            # Technical indicators
            prices = data['close'].tail(20).values
            sma_20 = np.mean(prices)
            current_price = prices[-1]
            price_vs_sma = (current_price - sma_20) / sma_20
            
            # Combine sentiment factors
            base_sentiment = 0.5 + momentum * 5
            technical_sentiment = 0.5 + price_vs_sma * 2
            sentiment_score = max(0, min(1, (base_sentiment + technical_sentiment) * volume_factor * 0.5))
            
            # Market regime detection
            if len(returns) >= 30:
                short_vol = np.std(returns[-10:])
                long_vol = np.std(returns[-30:])
                short_trend = np.mean(returns[-10:])
                
                if short_trend > 0.001 and short_vol < long_vol:
                    regime_confidence = 0.85
                elif short_trend < -0.001 and short_vol > long_vol:
                    regime_confidence = 0.75
                else:
                    regime_confidence = 0.65
            else:
                regime_confidence = 0.6
            
            # Check thresholds (adjusted for more realistic signal generation)
            if sentiment_score < 0.5:  # Lowered from 0.6
                return None
            if risk_score > 0.08:  # Increased from 0.05
                return None
            if regime_confidence < 0.6:  # Lowered from 0.7
                return None
            
            # Calculate confidence
            confidence = (sentiment_score * 0.4 + (1 - risk_score) * 0.3 + regime_confidence * 0.3)

            if confidence < 0.6:  # Lowered from 0.7
                return None
            
            # Create signal
            signal = TradingSignal(
                symbol=symbol,
                action='BUY',
                confidence=confidence,
                sentiment_score=sentiment_score,
                risk_score=risk_score,
                regime_confidence=regime_confidence,
                position_size=min(500.0, confidence * 500.0)
            )
            
            return signal
            
        except Exception as e:
            logger.error(f"Error generating signal for {symbol}: {e}")
            return None
    
    async def _analyze_market_context(self) -> Dict[str, Any]:
        """Analyze overall market context"""
        try:
            # Get SPY data for market context
            import yfinance as yf
            spy = yf.Ticker("SPY")
            spy_hist = spy.history(period="30d")
            
            if spy_hist.empty:
                return {'error': 'Cannot get market data'}
            
            spy_returns = spy_hist['Close'].pct_change().dropna().values
            
            market_context = {
                'spy_current_price': float(spy_hist['Close'].iloc[-1]),
                'spy_30d_return': float((spy_hist['Close'].iloc[-1] / spy_hist['Close'].iloc[0] - 1) * 100),
                'spy_volatility': float(np.std(spy_returns) * np.sqrt(252) * 100),
                'market_regime': 'Bull' if np.mean(spy_returns[-10:]) > 0 else 'Bear'
            }
            
            return market_context
            
        except Exception as e:
            logger.error(f"Error analyzing market context: {e}")
            return {'error': str(e)}
    
    async def _check_system_health(self) -> Dict[str, Any]:
        """Check system health and performance"""
        try:
            # Get account info
            account_info = await simulation_client.get_account_info()
            
            # Calculate recent performance
            performance = await simulation_client.get_portfolio_performance()
            
            health_status = {
                'account_equity': account_info.get('equity', 0),
                'cash_available': account_info.get('cash', 0),
                'active_positions': account_info.get('positions_count', 0),
                'total_pl': performance.get('total_pl', 0),
                'win_rate': performance.get('win_rate', 0),
                'system_uptime': (datetime.utcnow() - datetime.combine(self.system_start_date, time())).total_seconds() / 86400,
                'status': 'HEALTHY'
            }
            
            # Check for alerts
            if performance.get('win_rate', 0) < self.min_win_rate:
                health_status['status'] = 'WARNING'
                health_status['alert'] = f"Win rate below {self.min_win_rate}%"
            
            if performance.get('total_pl_pct', 0) < self.min_daily_return:
                health_status['status'] = 'CRITICAL'
                health_status['alert'] = f"Daily loss exceeds {abs(self.min_daily_return)}%"
            
            return health_status
            
        except Exception as e:
            logger.error(f"Error checking system health: {e}")
            return {'error': str(e), 'status': 'ERROR'}
    
    async def execute_daily_trading_cycle(self) -> Dict[str, Any]:
        """Execute complete daily trading cycle"""
        logger.info("🌅 Starting Daily Trading Cycle")
        cycle_start = datetime.utcnow()
        
        cycle_results = {
            'date': cycle_start.date().isoformat(),
            'cycle_start': cycle_start.isoformat(),
            'pre_market_analysis': {},
            'trade_executions': [],
            'performance_monitoring': {},
            'alerts': [],
            'cycle_summary': {}
        }
        
        try:
            # 1. Pre-market analysis
            analysis_results = await self.run_pre_market_analysis()
            cycle_results['pre_market_analysis'] = analysis_results
            
            # 2. Execute trading signals
            signals = analysis_results.get('trading_signals', [])
            executions = []
            
            for signal_data in signals:
                signal = TradingSignal(**signal_data)
                execution = await simulation_client.execute_trade(signal)
                executions.append(execution.to_dict())
            
            cycle_results['trade_executions'] = executions
            
            # 3. Monitor performance
            performance = await simulation_client.get_portfolio_performance()
            cycle_results['performance_monitoring'] = performance
            
            # 4. Check for alerts
            alerts = await self._check_performance_alerts(performance)
            cycle_results['alerts'] = alerts
            
            # 5. Update performance history
            await self._update_performance_history(cycle_results)
            
            # 6. Generate cycle summary
            cycle_duration = (datetime.utcnow() - cycle_start).total_seconds()
            successful_trades = len([e for e in executions if e['status'] == 'FILLED'])
            
            cycle_results['cycle_summary'] = {
                'cycle_duration_seconds': cycle_duration,
                'signals_generated': len(signals),
                'trades_executed': len(executions),
                'successful_trades': successful_trades,
                'alerts_triggered': len(alerts),
                'cycle_end': datetime.utcnow().isoformat()
            }
            
            # 7. Save daily results
            await self._save_daily_results(cycle_results)
            
            # 8. Send alerts if needed
            if alerts and self.email_alerts_enabled:
                await self._send_email_alerts(alerts, cycle_results)
            
            logger.info(f"✅ Daily trading cycle completed in {cycle_duration:.2f} seconds")
            
            return cycle_results
            
        except Exception as e:
            logger.error(f"❌ Error in daily trading cycle: {e}")
            cycle_results['error'] = str(e)
            return cycle_results
    
    async def _check_performance_alerts(self, performance: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check for performance-based alerts"""
        alerts = []
        
        try:
            # Win rate alert
            win_rate = performance.get('win_rate', 0)
            if win_rate < self.min_win_rate:
                alerts.append({
                    'type': 'WIN_RATE_LOW',
                    'severity': 'WARNING',
                    'message': f"Win rate ({win_rate:.1f}%) below threshold ({self.min_win_rate}%)",
                    'timestamp': datetime.utcnow().isoformat()
                })
            
            # Daily loss alert
            total_pl_pct = performance.get('total_pl_pct', 0)
            if total_pl_pct < self.min_daily_return:
                alerts.append({
                    'type': 'DAILY_LOSS_HIGH',
                    'severity': 'CRITICAL',
                    'message': f"Daily loss ({total_pl_pct:.2f}%) exceeds threshold ({self.min_daily_return}%)",
                    'timestamp': datetime.utcnow().isoformat()
                })
            
            # System health alert
            equity = performance.get('total_equity', 100000)
            if equity < 95000:  # 5% total loss
                alerts.append({
                    'type': 'EQUITY_LOW',
                    'severity': 'CRITICAL',
                    'message': f"Account equity (${equity:,.2f}) below 95% of initial capital",
                    'timestamp': datetime.utcnow().isoformat()
                })
            
        except Exception as e:
            logger.error(f"Error checking performance alerts: {e}")
        
        return alerts
    
    async def _save_daily_results(self, cycle_results: Dict[str, Any]):
        """Save daily results to JSON file"""
        try:
            timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
            filename = f"daily_results_{timestamp}.json"
            
            with open(filename, 'w') as f:
                json.dump(cycle_results, f, indent=2, default=str)
            
            logger.info(f"📄 Daily results saved to: {filename}")
            
        except Exception as e:
            logger.error(f"Error saving daily results: {e}")
    
    async def _update_performance_history(self, cycle_results: Dict[str, Any]):
        """Update rolling 30-day performance history"""
        try:
            performance = cycle_results.get('performance_monitoring', {})
            
            daily_record = {
                'date': cycle_results['date'],
                'total_equity': performance.get('total_equity', 0),
                'total_pl': performance.get('total_pl', 0),
                'total_pl_pct': performance.get('total_pl_pct', 0),
                'win_rate': performance.get('win_rate', 0),
                'trades_executed': cycle_results['cycle_summary']['trades_executed']
            }
            
            self.rolling_30day_performance.append(daily_record)
            
            # Keep only last 30 days
            if len(self.rolling_30day_performance) > 30:
                self.rolling_30day_performance = self.rolling_30day_performance[-30:]
            
            # Save performance history
            with open('performance_history.json', 'w') as f:
                json.dump(self.rolling_30day_performance, f, indent=2, default=str)
            
        except Exception as e:
            logger.error(f"Error updating performance history: {e}")
    
    async def _load_performance_history(self):
        """Load existing performance history"""
        try:
            if os.path.exists('performance_history.json'):
                with open('performance_history.json', 'r') as f:
                    self.rolling_30day_performance = json.load(f)
                logger.info(f"📊 Loaded {len(self.rolling_30day_performance)} days of performance history")
        except Exception as e:
            logger.warning(f"Could not load performance history: {e}")
    
    async def generate_performance_dashboard(self) -> Dict[str, Any]:
        """Generate 30-day rolling performance dashboard"""
        try:
            if not self.rolling_30day_performance:
                return {'error': 'No performance history available'}
            
            df = pd.DataFrame(self.rolling_30day_performance)
            
            dashboard = {
                'period_days': len(df),
                'start_date': df['date'].iloc[0] if len(df) > 0 else None,
                'end_date': df['date'].iloc[-1] if len(df) > 0 else None,
                'total_return_pct': df['total_pl_pct'].iloc[-1] if len(df) > 0 else 0,
                'avg_daily_return': df['total_pl_pct'].diff().mean() if len(df) > 1 else 0,
                'volatility': df['total_pl_pct'].diff().std() * np.sqrt(252) if len(df) > 1 else 0,
                'max_drawdown': self._calculate_max_drawdown(df['total_equity'].values),
                'current_win_rate': df['win_rate'].iloc[-1] if len(df) > 0 else 0,
                'total_trades': df['trades_executed'].sum(),
                'avg_trades_per_day': df['trades_executed'].mean(),
                'best_day': df['total_pl_pct'].diff().max() if len(df) > 1 else 0,
                'worst_day': df['total_pl_pct'].diff().min() if len(df) > 1 else 0
            }
            
            # Calculate Sharpe ratio
            if len(df) > 1:
                daily_returns = df['total_pl_pct'].diff().dropna()
                dashboard['sharpe_ratio'] = daily_returns.mean() / daily_returns.std() * np.sqrt(252) if daily_returns.std() > 0 else 0
            else:
                dashboard['sharpe_ratio'] = 0
            
            return dashboard
            
        except Exception as e:
            logger.error(f"Error generating performance dashboard: {e}")
            return {'error': str(e)}
    
    def _calculate_max_drawdown(self, equity_values: np.ndarray) -> float:
        """Calculate maximum drawdown from equity curve"""
        try:
            peak = equity_values[0]
            max_dd = 0
            
            for value in equity_values:
                if value > peak:
                    peak = value
                dd = (peak - value) / peak
                if dd > max_dd:
                    max_dd = dd
            
            return max_dd * 100
            
        except Exception:
            return 0.0

    async def _send_email_alerts(self, alerts: List[Dict[str, Any]], cycle_results: Dict[str, Any]):
        """Send email alerts for critical issues"""
        try:
            if not self.email_user or not self.email_password or not self.alert_recipients:
                logger.warning("Email configuration incomplete, skipping email alerts")
                return

            # Create email content
            subject = f"QuantumEdge Alert - {len(alerts)} issues detected"

            body = f"""QuantumEdge Automated Trading System Alert

Date: {cycle_results['date']}
Time: {datetime.utcnow().strftime('%H:%M:%S UTC')}

ALERTS TRIGGERED:
"""

            for alert in alerts:
                body += f"\n- {alert['severity']}: {alert['message']}"

            performance = cycle_results.get('performance_monitoring', {})
            body += f"""

CURRENT PERFORMANCE:
- Total Equity: ${performance.get('total_equity', 0):,.2f}
- Total P&L: ${performance.get('total_pl', 0):,.2f} ({performance.get('total_pl_pct', 0):+.2f}%)
- Win Rate: {performance.get('win_rate', 0):.1f}%
- Active Positions: {performance.get('total_positions', 0)}

TRADING ACTIVITY:
- Signals Generated: {cycle_results['cycle_summary']['signals_generated']}
- Trades Executed: {cycle_results['cycle_summary']['trades_executed']}
- Successful Trades: {cycle_results['cycle_summary']['successful_trades']}

Please review the system and take appropriate action if needed.

QuantumEdge Automated System
"""

            # Send email (simplified for demo)
            logger.info(f"📧 Email alert would be sent: {subject}")
            logger.info(f"📧 Recipients: {self.alert_recipients}")

        except Exception as e:
            logger.error(f"Error sending email alerts: {e}")

    def is_trading_day(self, date: datetime = None) -> bool:
        """Check if given date is a trading day (Monday-Friday, excluding holidays)"""
        if date is None:
            date = datetime.utcnow()

        # Check if it's a weekday (Monday=0, Friday=4)
        if date.weekday() >= 5:  # Saturday=5, Sunday=6
            return False

        # TODO: Add holiday checking logic here
        # For now, just check weekdays
        return True

    def should_run_premarket_analysis(self) -> bool:
        """Check if we should run pre-market analysis now"""
        now = datetime.utcnow()

        # Check if it's a trading day
        if not self.is_trading_day(now):
            return False

        # Convert to Eastern Time for market hours
        import pytz
        eastern = pytz.timezone('US/Eastern')
        eastern_time = now.astimezone(eastern)

        # Pre-market analysis should run between 6:00 AM and 9:30 AM ET
        market_open = time(9, 30)  # 9:30 AM ET
        premarket_start = time(6, 0)  # 6:00 AM ET

        current_time = eastern_time.time()

        return premarket_start <= current_time <= market_open

# Global automated system
automated_system = AutomatedDailySystem()

async def initialize_automated_system():
    """Initialize the automated daily system"""
    await automated_system.initialize()
    logger.info("Automated daily system initialized")
