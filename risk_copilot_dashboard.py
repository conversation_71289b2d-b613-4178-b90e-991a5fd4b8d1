"""
Dynamic Risk Co-Pilot - Streamlit Dashboard
User-facing web interface for portfolio risk assessment
"""

import streamlit as st
import asyncio
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import sys
import os

# Add src to path
sys.path.append('src')

from src.omnidata_fusion_engine import omnidata_engine
from src.reddit_sentiment_engine import reddit_sentiment_engine
from src.satellite_data_engine import satellite_engine

# Configure Streamlit page
st.set_page_config(
    page_title="Dynamic Risk Co-Pilot",
    page_icon="🛡️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .risk-score-high {
        background-color: #ff4444;
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        text-align: center;
        font-size: 1.5rem;
        font-weight: bold;
    }
    .risk-score-medium {
        background-color: #ffaa00;
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        text-align: center;
        font-size: 1.5rem;
        font-weight: bold;
    }
    .risk-score-low {
        background-color: #44aa44;
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        text-align: center;
        font-size: 1.5rem;
        font-weight: bold;
    }
    .alert-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data(ttl=300)  # Cache for 5 minutes
def run_async_function(func, *args):
    """Helper to run async functions in Streamlit"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(func(*args))
    finally:
        loop.close()

def initialize_engines():
    """Initialize all engines"""
    try:
        # Initialize engines
        reddit_sentiment_engine.initialize_reddit_client()
        satellite_engine.initialize_sentinel_hub()
        run_async_function(omnidata_engine.train_risk_model)
        return True
    except Exception as e:
        st.error(f"Error initializing engines: {e}")
        return False

def display_risk_score(symbol, risk_score, risk_level):
    """Display risk score with color coding"""
    if risk_level == 'critical' or risk_score > 8:
        st.markdown(f'<div class="risk-score-high">🚨 {symbol}: {risk_score:.1f}/10 - CRITICAL RISK</div>', unsafe_allow_html=True)
    elif risk_level == 'high' or risk_score > 6:
        st.markdown(f'<div class="risk-score-medium">⚠️ {symbol}: {risk_score:.1f}/10 - HIGH RISK</div>', unsafe_allow_html=True)
    elif risk_level == 'medium' or risk_score > 4:
        st.markdown(f'<div class="risk-score-medium">🟡 {symbol}: {risk_score:.1f}/10 - MEDIUM RISK</div>', unsafe_allow_html=True)
    else:
        st.markdown(f'<div class="risk-score-low">✅ {symbol}: {risk_score:.1f}/10 - LOW RISK</div>', unsafe_allow_html=True)

def create_risk_gauge(risk_score, symbol):
    """Create a gauge chart for risk score"""
    fig = go.Figure(go.Indicator(
        mode = "gauge+number+delta",
        value = risk_score,
        domain = {'x': [0, 1], 'y': [0, 1]},
        title = {'text': f"{symbol} Risk Score"},
        delta = {'reference': 5},
        gauge = {
            'axis': {'range': [None, 10]},
            'bar': {'color': "darkblue"},
            'steps': [
                {'range': [0, 3], 'color': "lightgreen"},
                {'range': [3, 6], 'color': "yellow"},
                {'range': [6, 8], 'color': "orange"},
                {'range': [8, 10], 'color': "red"}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': 8
            }
        }
    ))
    
    fig.update_layout(height=300)
    return fig

def create_portfolio_overview(assessments):
    """Create portfolio overview chart"""
    if not assessments:
        return None
    
    symbols = list(assessments.keys())
    risk_scores = [assessments[symbol].risk_score for symbol in symbols]
    risk_levels = [assessments[symbol].risk_level for symbol in symbols]
    
    # Color mapping
    color_map = {'low': 'green', 'medium': 'yellow', 'high': 'orange', 'critical': 'red'}
    colors = [color_map.get(level, 'gray') for level in risk_levels]
    
    fig = go.Figure(data=[
        go.Bar(
            x=symbols,
            y=risk_scores,
            marker_color=colors,
            text=[f"{score:.1f}" for score in risk_scores],
            textposition='auto',
        )
    ])
    
    fig.update_layout(
        title="Portfolio Risk Overview",
        xaxis_title="Symbols",
        yaxis_title="Risk Score (1-10)",
        yaxis=dict(range=[0, 10]),
        height=400
    )
    
    return fig

def main():
    """Main Streamlit application"""
    
    # Header
    st.markdown('<h1 class="main-header">🛡️ Dynamic Risk Co-Pilot</h1>', unsafe_allow_html=True)
    st.markdown("### Protect your portfolio with AI-powered omnidata risk analysis")
    
    # Sidebar
    st.sidebar.header("🔧 Configuration")
    
    # Initialize engines
    if 'engines_initialized' not in st.session_state:
        with st.spinner("Initializing Risk Co-Pilot engines..."):
            st.session_state.engines_initialized = initialize_engines()
    
    if not st.session_state.engines_initialized:
        st.error("❌ Failed to initialize engines. Please refresh the page.")
        return
    
    # Portfolio input
    st.sidebar.subheader("📊 Your Portfolio")
    
    # Default portfolio
    default_symbols = "AAPL, MSFT, GOOGL, AMZN, TSLA"
    portfolio_input = st.sidebar.text_area(
        "Enter stock symbols (comma-separated):",
        value=default_symbols,
        help="Enter stock symbols separated by commas (e.g., AAPL, MSFT, GOOGL)"
    )
    
    # Parse portfolio
    portfolio_symbols = [symbol.strip().upper() for symbol in portfolio_input.split(',') if symbol.strip()]
    
    if len(portfolio_symbols) > 10:
        st.sidebar.warning("⚠️ Maximum 10 symbols allowed for optimal performance")
        portfolio_symbols = portfolio_symbols[:10]
    
    # Analysis settings
    st.sidebar.subheader("⚙️ Analysis Settings")
    
    risk_threshold = st.sidebar.slider(
        "Risk Alert Threshold",
        min_value=1.0,
        max_value=10.0,
        value=6.0,
        step=0.5,
        help="Get alerts when risk score exceeds this threshold"
    )
    
    auto_refresh = st.sidebar.checkbox(
        "Auto-refresh (5 min)",
        value=False,
        help="Automatically refresh risk analysis every 5 minutes"
    )
    
    # Main content
    if st.button("🔍 Analyze Portfolio Risk", type="primary") or auto_refresh:
        
        if not portfolio_symbols:
            st.warning("⚠️ Please enter at least one stock symbol")
            return
        
        # Progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        try:
            # Run risk assessment
            status_text.text("🔄 Analyzing portfolio risk...")
            progress_bar.progress(20)
            
            assessments = run_async_function(omnidata_engine.assess_portfolio_risk, portfolio_symbols)
            progress_bar.progress(100)
            status_text.text("✅ Analysis complete!")
            
            if not assessments:
                st.error("❌ No risk assessments generated. Please check your symbols and try again.")
                return
            
            # Clear progress indicators
            progress_bar.empty()
            status_text.empty()
            
            # Display results
            st.header("📊 Portfolio Risk Analysis Results")
            
            # Portfolio overview
            col1, col2 = st.columns([2, 1])
            
            with col1:
                portfolio_chart = create_portfolio_overview(assessments)
                if portfolio_chart:
                    st.plotly_chart(portfolio_chart, use_container_width=True)
            
            with col2:
                # Portfolio summary
                total_symbols = len(assessments)
                avg_risk = sum(assessment.risk_score for assessment in assessments.values()) / total_symbols
                high_risk_count = sum(1 for assessment in assessments.values() if assessment.risk_score > risk_threshold)
                
                st.metric("Total Symbols", total_symbols)
                st.metric("Average Risk Score", f"{avg_risk:.1f}/10")
                st.metric("High Risk Alerts", high_risk_count)
                
                # Overall portfolio status
                if avg_risk > 7:
                    st.error("🚨 HIGH PORTFOLIO RISK")
                elif avg_risk > 5:
                    st.warning("⚠️ MODERATE PORTFOLIO RISK")
                else:
                    st.success("✅ LOW PORTFOLIO RISK")
            
            # Individual symbol analysis
            st.header("🔍 Individual Symbol Analysis")
            
            for symbol, assessment in assessments.items():
                with st.expander(f"📈 {symbol} - Risk Score: {assessment.risk_score:.1f}/10", expanded=assessment.risk_score > risk_threshold):
                    
                    col1, col2 = st.columns([1, 2])
                    
                    with col1:
                        # Risk gauge
                        gauge_chart = create_risk_gauge(assessment.risk_score, symbol)
                        st.plotly_chart(gauge_chart, use_container_width=True)
                    
                    with col2:
                        # Risk details
                        display_risk_score(symbol, assessment.risk_score, assessment.risk_level)
                        
                        st.subheader("🎯 Contributing Factors")
                        factors_df = pd.DataFrame([
                            {"Factor": factor, "Impact": impact}
                            for factor, impact in assessment.contributing_factors.items()
                        ])
                        st.dataframe(factors_df, use_container_width=True)
                        
                        # Alerts
                        if assessment.alerts:
                            st.subheader("🚨 Risk Alerts")
                            for alert in assessment.alerts:
                                st.markdown(f'<div class="alert-box">⚠️ {alert}</div>', unsafe_allow_html=True)
                        
                        # Recommendations
                        if assessment.recommendations:
                            st.subheader("💡 Recommendations")
                            for rec in assessment.recommendations:
                                st.info(f"💡 {rec}")
            
            # Data sources status
            st.header("📡 Data Sources Status")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.subheader("📈 Financial Data")
                st.success("✅ yfinance - Active")
                st.caption("Real-time stock prices and technical indicators")
            
            with col2:
                st.subheader("💬 Sentiment Data")
                st.info("📊 Reddit r/wallstreetbets - Simulated")
                st.caption("Social sentiment analysis (demo mode)")
            
            with col3:
                st.subheader("🛰️ Satellite Data")
                st.info("📡 Port of LA Ship Counts - Simulated")
                st.caption("Supply chain indicators (demo mode)")
            
            # Timestamp
            st.caption(f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
        except Exception as e:
            st.error(f"❌ Error during analysis: {e}")
            st.exception(e)
    
    # Information section
    st.sidebar.markdown("---")
    st.sidebar.subheader("ℹ️ About")
    st.sidebar.info("""
    **Dynamic Risk Co-Pilot** uses AI to analyze:
    
    🔹 **Financial Data**: Price, volatility, beta
    🔹 **Sentiment Data**: Social media sentiment
    🔹 **Satellite Data**: Supply chain indicators
    
    **Risk Scores (1-10):**
    - 1-3: Low Risk ✅
    - 4-6: Medium Risk ⚠️
    - 7-8: High Risk 🔶
    - 9-10: Critical Risk 🚨
    
    *This tool provides risk insights only. Not financial advice.*
    """)
    
    # Auto-refresh
    if auto_refresh:
        import time
        time.sleep(300)  # 5 minutes
        st.rerun()

if __name__ == "__main__":
    main()
