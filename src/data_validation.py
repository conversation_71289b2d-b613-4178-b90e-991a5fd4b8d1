"""
QuantumEdge Financial Intelligence Tool - Data Validation Framework
Comprehensive data quality validation with 99%+ accuracy requirements
"""

import logging
import numpy as np
import polars as pl
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger('quantumedge.validation')

class ValidationSeverity(Enum):
    """Validation check severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class ValidationCheck:
    """Individual validation check result"""
    check_name: str
    score: float  # 0.0 to 1.0
    severity: ValidationSeverity
    message: str
    details: Optional[Dict] = None

@dataclass
class ValidationResult:
    """Complete validation result for a dataset"""
    data_type: str
    symbol: Optional[str]
    timestamp: datetime
    quality_score: float  # Overall score 0.0 to 1.0
    checks: List[ValidationCheck]
    passed: bool
    record_count: int
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for storage"""
        return {
            'data_type': self.data_type,
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'quality_score': self.quality_score,
            'passed': self.passed,
            'record_count': self.record_count,
            'checks': [
                {
                    'name': check.check_name,
                    'score': check.score,
                    'severity': check.severity.value,
                    'message': check.message,
                    'details': check.details
                }
                for check in self.checks
            ]
        }

class DataValidator:
    """Comprehensive data validation framework"""
    
    def __init__(self, quality_threshold: float = 0.95):
        self.quality_threshold = quality_threshold
        self.logger = logger
    
    def validate_market_data(self, data: Dict, symbol: str) -> ValidationResult:
        """Validate market data (OHLCV) with comprehensive checks"""
        checks = []
        
        # Basic structure validation
        checks.append(self._check_required_fields(data, ['open', 'high', 'low', 'close', 'volume']))
        
        # Price continuity validation
        checks.append(self._check_price_continuity(data))
        
        # Volume validation
        checks.append(self._check_volume_validity(data))
        
        # Outlier detection
        checks.append(self._check_price_outliers(data, symbol))
        
        # Timestamp validation
        checks.append(self._check_timestamp_validity(data))
        
        # Calculate overall quality score
        quality_score = sum(check.score for check in checks) / len(checks)
        passed = quality_score >= self.quality_threshold
        
        return ValidationResult(
            data_type='market_data',
            symbol=symbol,
            timestamp=datetime.utcnow(),
            quality_score=quality_score,
            checks=checks,
            passed=passed,
            record_count=1
        )
    
    def validate_news_data(self, data: List[Dict], symbol: str) -> ValidationResult:
        """Validate news data with sentiment analysis"""
        checks = []
        
        # Data completeness
        checks.append(self._check_news_completeness(data))
        
        # Content quality
        checks.append(self._check_news_content_quality(data))
        
        # Timestamp validation
        checks.append(self._check_news_timestamps(data))
        
        # Duplicate detection
        checks.append(self._check_news_duplicates(data))
        
        # Language validation
        checks.append(self._check_news_language(data))
        
        quality_score = sum(check.score for check in checks) / len(checks)
        passed = quality_score >= self.quality_threshold
        
        return ValidationResult(
            data_type='news_data',
            symbol=symbol,
            timestamp=datetime.utcnow(),
            quality_score=quality_score,
            checks=checks,
            passed=passed,
            record_count=len(data)
        )
    
    def validate_sentiment_data(self, data: Dict, symbol: str) -> ValidationResult:
        """Validate sentiment analysis results"""
        checks = []
        
        # Sentiment score range validation
        checks.append(self._check_sentiment_range(data))
        
        # Confidence validation
        checks.append(self._check_sentiment_confidence(data))
        
        # Source validation
        checks.append(self._check_sentiment_source(data))
        
        quality_score = sum(check.score for check in checks) / len(checks)
        passed = quality_score >= self.quality_threshold
        
        return ValidationResult(
            data_type='sentiment_data',
            symbol=symbol,
            timestamp=datetime.utcnow(),
            quality_score=quality_score,
            checks=checks,
            passed=passed,
            record_count=1
        )
    
    def _check_required_fields(self, data: Dict, required_fields: List[str]) -> ValidationCheck:
        """Check if all required fields are present"""
        missing_fields = [field for field in required_fields if field not in data]
        
        if not missing_fields:
            return ValidationCheck(
                check_name='required_fields',
                score=1.0,
                severity=ValidationSeverity.INFO,
                message='All required fields present'
            )
        else:
            return ValidationCheck(
                check_name='required_fields',
                score=0.0,
                severity=ValidationSeverity.CRITICAL,
                message=f'Missing required fields: {missing_fields}',
                details={'missing_fields': missing_fields}
            )
    
    def _check_price_continuity(self, data: Dict) -> ValidationCheck:
        """Validate OHLC price relationships"""
        try:
            open_price = float(data.get('open', 0))
            high_price = float(data.get('high', 0))
            low_price = float(data.get('low', 0))
            close_price = float(data.get('close', 0))
            
            # Check basic OHLC relationships
            if not (low_price <= open_price <= high_price and 
                   low_price <= close_price <= high_price):
                return ValidationCheck(
                    check_name='price_continuity',
                    score=0.0,
                    severity=ValidationSeverity.ERROR,
                    message='Invalid OHLC price relationships',
                    details={
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price
                    }
                )
            
            # Check for extreme price movements (>50% in single period)
            if open_price > 0:
                price_change = abs(close_price - open_price) / open_price
                if price_change > 0.50:
                    return ValidationCheck(
                        check_name='price_continuity',
                        score=0.3,
                        severity=ValidationSeverity.WARNING,
                        message=f'Extreme price movement: {price_change:.2%}',
                        details={'price_change_percent': price_change}
                    )
            
            return ValidationCheck(
                check_name='price_continuity',
                score=1.0,
                severity=ValidationSeverity.INFO,
                message='Price continuity valid'
            )
            
        except (ValueError, TypeError) as e:
            return ValidationCheck(
                check_name='price_continuity',
                score=0.0,
                severity=ValidationSeverity.ERROR,
                message=f'Price data type error: {str(e)}'
            )
    
    def _check_volume_validity(self, data: Dict) -> ValidationCheck:
        """Validate volume data"""
        try:
            volume = float(data.get('volume', 0))
            
            if volume < 0:
                return ValidationCheck(
                    check_name='volume_validity',
                    score=0.0,
                    severity=ValidationSeverity.ERROR,
                    message='Negative volume detected',
                    details={'volume': volume}
                )
            
            if volume == 0:
                return ValidationCheck(
                    check_name='volume_validity',
                    score=0.5,
                    severity=ValidationSeverity.WARNING,
                    message='Zero volume detected',
                    details={'volume': volume}
                )
            
            return ValidationCheck(
                check_name='volume_validity',
                score=1.0,
                severity=ValidationSeverity.INFO,
                message='Volume data valid'
            )
            
        except (ValueError, TypeError) as e:
            return ValidationCheck(
                check_name='volume_validity',
                score=0.0,
                severity=ValidationSeverity.ERROR,
                message=f'Volume data type error: {str(e)}'
            )
    
    def _check_price_outliers(self, data: Dict, symbol: str) -> ValidationCheck:
        """Detect price outliers using statistical methods"""
        try:
            close_price = float(data.get('close', 0))
            
            # For now, use simple range checks
            # In production, this would use historical data for z-score analysis
            if close_price <= 0:
                return ValidationCheck(
                    check_name='price_outliers',
                    score=0.0,
                    severity=ValidationSeverity.ERROR,
                    message='Invalid price: zero or negative',
                    details={'close_price': close_price}
                )
            
            # Check for extremely high prices (likely data error)
            if close_price > 100000:  # $100k per share
                return ValidationCheck(
                    check_name='price_outliers',
                    score=0.2,
                    severity=ValidationSeverity.WARNING,
                    message='Extremely high price detected',
                    details={'close_price': close_price}
                )
            
            return ValidationCheck(
                check_name='price_outliers',
                score=1.0,
                severity=ValidationSeverity.INFO,
                message='No price outliers detected'
            )
            
        except (ValueError, TypeError) as e:
            return ValidationCheck(
                check_name='price_outliers',
                score=0.0,
                severity=ValidationSeverity.ERROR,
                message=f'Price outlier check failed: {str(e)}'
            )
    
    def _check_timestamp_validity(self, data: Dict) -> ValidationCheck:
        """Validate timestamp data"""
        timestamp = data.get('timestamp') or data.get('t') or data.get('datetime')
        
        if timestamp is None:
            return ValidationCheck(
                check_name='timestamp_validity',
                score=0.0,
                severity=ValidationSeverity.ERROR,
                message='No timestamp found in data'
            )
        
        try:
            # Handle different timestamp formats
            if isinstance(timestamp, (int, float)):
                # Unix timestamp (milliseconds or seconds)
                if timestamp > 1e12:  # Milliseconds
                    dt = datetime.fromtimestamp(timestamp / 1000)
                else:  # Seconds
                    dt = datetime.fromtimestamp(timestamp)
            elif isinstance(timestamp, str):
                # ISO format string
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                dt = timestamp
            
            # Check if timestamp is reasonable (not too old or in future)
            now = datetime.utcnow()
            if dt > now + timedelta(hours=1):
                return ValidationCheck(
                    check_name='timestamp_validity',
                    score=0.3,
                    severity=ValidationSeverity.WARNING,
                    message='Timestamp in future',
                    details={'timestamp': dt.isoformat()}
                )
            
            if dt < now - timedelta(days=365):
                return ValidationCheck(
                    check_name='timestamp_validity',
                    score=0.8,
                    severity=ValidationSeverity.INFO,
                    message='Old timestamp (>1 year)',
                    details={'timestamp': dt.isoformat()}
                )
            
            return ValidationCheck(
                check_name='timestamp_validity',
                score=1.0,
                severity=ValidationSeverity.INFO,
                message='Timestamp valid'
            )
            
        except Exception as e:
            return ValidationCheck(
                check_name='timestamp_validity',
                score=0.0,
                severity=ValidationSeverity.ERROR,
                message=f'Timestamp parsing error: {str(e)}'
            )
    
    def _check_news_completeness(self, data: List[Dict]) -> ValidationCheck:
        """Check news data completeness"""
        if not data:
            return ValidationCheck(
                check_name='news_completeness',
                score=0.0,
                severity=ValidationSeverity.WARNING,
                message='No news data provided'
            )
        
        required_fields = ['headline', 'summary', 'url', 'datetime']
        complete_articles = 0
        
        for article in data:
            if all(field in article and article[field] for field in required_fields):
                complete_articles += 1
        
        completeness_ratio = complete_articles / len(data)
        
        if completeness_ratio >= 0.9:
            score = 1.0
            severity = ValidationSeverity.INFO
            message = f'High completeness: {completeness_ratio:.1%}'
        elif completeness_ratio >= 0.7:
            score = 0.7
            severity = ValidationSeverity.WARNING
            message = f'Moderate completeness: {completeness_ratio:.1%}'
        else:
            score = 0.3
            severity = ValidationSeverity.ERROR
            message = f'Low completeness: {completeness_ratio:.1%}'
        
        return ValidationCheck(
            check_name='news_completeness',
            score=score,
            severity=severity,
            message=message,
            details={'completeness_ratio': completeness_ratio}
        )
    
    def _check_news_content_quality(self, data: List[Dict]) -> ValidationCheck:
        """Check news content quality"""
        if not data:
            return ValidationCheck(
                check_name='news_content_quality',
                score=0.0,
                severity=ValidationSeverity.WARNING,
                message='No news data to validate'
            )
        
        quality_issues = 0
        
        for article in data:
            headline = article.get('headline', '')
            summary = article.get('summary', '')
            
            # Check for minimum content length
            if len(headline) < 10 or len(summary) < 50:
                quality_issues += 1
            
            # Check for spam indicators
            spam_indicators = ['click here', 'buy now', '!!!', 'FREE', 'URGENT']
            if any(indicator.lower() in headline.lower() for indicator in spam_indicators):
                quality_issues += 1
        
        quality_ratio = 1 - (quality_issues / len(data))
        
        return ValidationCheck(
            check_name='news_content_quality',
            score=quality_ratio,
            severity=ValidationSeverity.INFO if quality_ratio > 0.8 else ValidationSeverity.WARNING,
            message=f'Content quality: {quality_ratio:.1%}',
            details={'quality_issues': quality_issues, 'total_articles': len(data)}
        )
    
    def _check_news_timestamps(self, data: List[Dict]) -> ValidationCheck:
        """Validate news article timestamps"""
        if not data:
            return ValidationCheck(
                check_name='news_timestamps',
                score=0.0,
                severity=ValidationSeverity.WARNING,
                message='No news data to validate'
            )
        
        valid_timestamps = 0
        
        for article in data:
            timestamp = article.get('datetime')
            if timestamp:
                try:
                    if isinstance(timestamp, (int, float)):
                        dt = datetime.fromtimestamp(timestamp)
                    else:
                        dt = datetime.fromisoformat(str(timestamp).replace('Z', '+00:00'))
                    
                    # Check if timestamp is reasonable
                    now = datetime.utcnow()
                    if now - timedelta(days=30) <= dt <= now + timedelta(hours=1):
                        valid_timestamps += 1
                except:
                    pass
        
        validity_ratio = valid_timestamps / len(data)
        
        return ValidationCheck(
            check_name='news_timestamps',
            score=validity_ratio,
            severity=ValidationSeverity.INFO if validity_ratio > 0.9 else ValidationSeverity.WARNING,
            message=f'Timestamp validity: {validity_ratio:.1%}',
            details={'valid_timestamps': valid_timestamps, 'total_articles': len(data)}
        )
    
    def _check_news_duplicates(self, data: List[Dict]) -> ValidationCheck:
        """Check for duplicate news articles"""
        if not data:
            return ValidationCheck(
                check_name='news_duplicates',
                score=1.0,
                severity=ValidationSeverity.INFO,
                message='No data to check for duplicates'
            )
        
        headlines = [article.get('headline', '') for article in data]
        unique_headlines = set(headlines)
        
        duplicate_ratio = 1 - (len(unique_headlines) / len(headlines))
        uniqueness_score = 1 - duplicate_ratio
        
        return ValidationCheck(
            check_name='news_duplicates',
            score=uniqueness_score,
            severity=ValidationSeverity.INFO if duplicate_ratio < 0.1 else ValidationSeverity.WARNING,
            message=f'Duplicate ratio: {duplicate_ratio:.1%}',
            details={'total_articles': len(data), 'unique_articles': len(unique_headlines)}
        )
    
    def _check_news_language(self, data: List[Dict]) -> ValidationCheck:
        """Basic language validation for news content"""
        if not data:
            return ValidationCheck(
                check_name='news_language',
                score=1.0,
                severity=ValidationSeverity.INFO,
                message='No data to validate language'
            )
        
        english_articles = 0
        
        for article in data:
            headline = article.get('headline', '')
            # Simple heuristic: check for common English words
            english_words = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']
            if any(word in headline.lower() for word in english_words):
                english_articles += 1
        
        english_ratio = english_articles / len(data)
        
        return ValidationCheck(
            check_name='news_language',
            score=english_ratio,
            severity=ValidationSeverity.INFO,
            message=f'English content ratio: {english_ratio:.1%}',
            details={'english_articles': english_articles, 'total_articles': len(data)}
        )
    
    def _check_sentiment_range(self, data: Dict) -> ValidationCheck:
        """Validate sentiment score is in valid range"""
        sentiment_score = data.get('sentiment_score')
        
        if sentiment_score is None:
            return ValidationCheck(
                check_name='sentiment_range',
                score=0.0,
                severity=ValidationSeverity.ERROR,
                message='No sentiment score found'
            )
        
        try:
            score = float(sentiment_score)
            if -1.0 <= score <= 1.0:
                return ValidationCheck(
                    check_name='sentiment_range',
                    score=1.0,
                    severity=ValidationSeverity.INFO,
                    message='Sentiment score in valid range'
                )
            else:
                return ValidationCheck(
                    check_name='sentiment_range',
                    score=0.0,
                    severity=ValidationSeverity.ERROR,
                    message=f'Sentiment score out of range: {score}',
                    details={'sentiment_score': score}
                )
        except (ValueError, TypeError):
            return ValidationCheck(
                check_name='sentiment_range',
                score=0.0,
                severity=ValidationSeverity.ERROR,
                message='Invalid sentiment score data type'
            )
    
    def _check_sentiment_confidence(self, data: Dict) -> ValidationCheck:
        """Validate sentiment confidence score"""
        confidence = data.get('confidence')
        
        if confidence is None:
            return ValidationCheck(
                check_name='sentiment_confidence',
                score=0.8,  # Not critical if missing
                severity=ValidationSeverity.INFO,
                message='No confidence score provided'
            )
        
        try:
            conf_score = float(confidence)
            if 0.0 <= conf_score <= 1.0:
                return ValidationCheck(
                    check_name='sentiment_confidence',
                    score=1.0,
                    severity=ValidationSeverity.INFO,
                    message='Confidence score valid'
                )
            else:
                return ValidationCheck(
                    check_name='sentiment_confidence',
                    score=0.5,
                    severity=ValidationSeverity.WARNING,
                    message=f'Confidence score out of range: {conf_score}'
                )
        except (ValueError, TypeError):
            return ValidationCheck(
                check_name='sentiment_confidence',
                score=0.5,
                severity=ValidationSeverity.WARNING,
                message='Invalid confidence score data type'
            )
    
    def _check_sentiment_source(self, data: Dict) -> ValidationCheck:
        """Validate sentiment analysis source"""
        source = data.get('source')
        
        if source is None:
            return ValidationCheck(
                check_name='sentiment_source',
                score=0.7,
                severity=ValidationSeverity.INFO,
                message='No source information provided'
            )
        
        valid_sources = ['news', 'reddit', 'twitter', 'social_media', 'financial_reports']
        if source.lower() in valid_sources:
            return ValidationCheck(
                check_name='sentiment_source',
                score=1.0,
                severity=ValidationSeverity.INFO,
                message='Valid sentiment source'
            )
        else:
            return ValidationCheck(
                check_name='sentiment_source',
                score=0.8,
                severity=ValidationSeverity.INFO,
                message=f'Unknown sentiment source: {source}'
            )
