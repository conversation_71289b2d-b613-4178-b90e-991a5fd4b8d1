#!/usr/bin/env python3
"""
Test Alpaca API connection
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

sys.path.append('src')

from src.alpaca_trading_client import alpaca_client

async def test_connection():
    try:
        print("🔍 Testing Alpaca API connection...")

        # Debug environment variables
        api_key = os.getenv('ALPACA_API_KEY')
        api_secret = os.getenv('ALPACA_API_SECRET')

        print(f"API Key found: {api_key[:8] + '...' if api_key else 'NOT_FOUND'}")
        print(f"API Secret found: {api_secret[:8] + '...' if api_secret else 'NOT_FOUND'}")

        await alpaca_client.initialize()
        
        account_info = await alpaca_client.get_account_info()
        
        if 'error' in account_info:
            print(f"❌ Connection failed: {account_info['error']}")
            return False
        
        print("✅ Alpaca connection successful!")
        print(f"📊 Account: {account_info.get('account_number', 'Unknown')}")
        print(f"💰 Equity: ${account_info.get('equity', 0):,.2f}")
        print(f"💵 Cash: ${account_info.get('cash', 0):,.2f}")
        print(f"🔄 Buying Power: ${account_info.get('buying_power', 0):,.2f}")
        print(f"📈 Portfolio Value: ${account_info.get('portfolio_value', 0):,.2f}")
        
        if account_info.get('positions'):
            print(f"📊 Current Positions: {len(account_info['positions'])}")
            for pos in account_info['positions']:
                print(f"   {pos['symbol']}: {pos['qty']} shares")
        else:
            print("📊 No current positions")
        
        return True
        
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_connection())
