"""
QuantumEdge Financial Intelligence Tool - Configuration Management
Optimized for M1 Max hardware with 64GB RAM and 32-core GPU
"""

import os
from typing import Dict, List, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class APIConfig:
    """API configuration with rate limiting"""
    name: str
    base_url: str
    api_key: Optional[str]
    rate_limit_per_minute: Optional[int]
    rate_limit_per_day: Optional[int]
    rate_limit_per_second: Optional[int]
    free_tier: bool = True

@dataclass
class DatabaseConfig:
    """Database configuration for DuckDB and SQLite"""
    duckdb_path: str = "data/analytics.duckdb"
    sqlite_path: str = "data/operational.db"
    redis_url: str = "redis://localhost:6379"
    connection_pool_size: int = 10

@dataclass
class HardwareConfig:
    """M1 Max hardware optimization settings"""
    max_concurrent_requests: int = 8  # Utilize 10-core CPU
    batch_processing_size: int = 1000  # Optimize for 64GB RAM
    gpu_acceleration: bool = True  # Use 32-core GPU
    memory_mapping: bool = True
    pytorch_mps_high_watermark_ratio: float = 0.8

class Config:
    """Main configuration class"""
    
    def __init__(self):
        self.apis = self._load_api_configs()
        self.database = DatabaseConfig()
        self.hardware = HardwareConfig()
        self.budget = self._load_budget_config()
        self.symbols = self._load_symbol_config()
    
    def _load_api_configs(self) -> Dict[str, APIConfig]:
        """Load API configurations from environment"""
        return {
            'polygon': APIConfig(
                name='Polygon.io',
                base_url='https://api.polygon.io',
                api_key=os.getenv('POLYGON_API_KEY'),
                rate_limit_per_minute=5,  # Free tier
                rate_limit_per_day=None,
                rate_limit_per_second=None,
                free_tier=True
            ),
            'alpha_vantage': APIConfig(
                name='Alpha Vantage',
                base_url='https://www.alphavantage.co',
                api_key=os.getenv('ALPHA_VANTAGE_API_KEY'),
                rate_limit_per_minute=5,
                rate_limit_per_day=25,  # Free tier
                rate_limit_per_second=None,
                free_tier=True
            ),
            'sec': APIConfig(
                name='SEC API',
                base_url='https://data.sec.gov',
                api_key=None,  # Public API
                rate_limit_per_minute=None,
                rate_limit_per_day=None,
                rate_limit_per_second=10,  # SEC enforced limit
                free_tier=True
            ),
            'reddit': APIConfig(
                name='Reddit API',
                base_url='https://oauth.reddit.com',
                api_key=None,  # OAuth required
                rate_limit_per_minute=100,
                rate_limit_per_day=None,
                rate_limit_per_second=None,
                free_tier=True
            )
        }
    
    def _load_budget_config(self) -> Dict:
        """Load budget and cost management configuration"""
        return {
            'total_annual_budget': 100.0,  # USD
            'monthly_budget': 8.33,  # USD
            'emergency_reserve': 20.0,  # USD
            'operational_budget': 80.0,  # USD
            'alert_thresholds': [0.5, 0.75, 0.9],  # 50%, 75%, 90%
            'cost_per_request': {
                'polygon': 0.0,  # Free tier
                'alpha_vantage': 0.0,  # Free tier
                'sec': 0.0,  # Free
                'reddit': 0.0  # Free with OAuth
            }
        }
    
    def _load_symbol_config(self) -> Dict:
        """Load symbol universe configuration"""
        return {
            'tier_1': {
                'description': 'S&P 500 - High Priority',
                'count': 500,
                'update_frequency_minutes': 120,  # 2 hours
                'data_sources': ['polygon', 'alpha_vantage', 'sec']
            },
            'tier_2': {
                'description': 'Russell 1000 Additional - Medium Priority',
                'count': 500,
                'update_frequency_minutes': 240,  # 4 hours
                'data_sources': ['polygon', 'sec']
            },
            'tier_3': {
                'description': 'Extended Universe - Low Priority',
                'count': 1000,
                'update_frequency_minutes': 1440,  # Daily
                'data_sources': ['polygon']
            }
        }
    
    def get_api_config(self, api_name: str) -> APIConfig:
        """Get API configuration by name"""
        if api_name not in self.apis:
            raise ValueError(f"Unknown API: {api_name}")
        return self.apis[api_name]
    
    def validate_api_keys(self) -> Dict[str, bool]:
        """Validate that required API keys are present"""
        validation_results = {}
        
        for api_name, config in self.apis.items():
            if config.api_key is None and api_name not in ['sec', 'reddit']:
                validation_results[api_name] = False
            else:
                validation_results[api_name] = True
        
        return validation_results

# Global configuration instance
config = Config()

# Data quality thresholds
DATA_QUALITY_THRESHOLDS = {
    'completeness': 0.95,
    'accuracy': 0.99,
    'consistency': 0.98,
    'timeliness': 900  # 15 minutes in seconds
}

# Performance targets for M1 Max
PERFORMANCE_TARGETS = {
    'api_response_time': 1.0,  # seconds
    'data_processing_throughput': 1000,  # records/second
    'memory_utilization': 0.8,  # 80% of 64GB
    'gpu_utilization': 0.7,  # 70% of 32-core GPU
    'uptime': 0.995  # 99.5%
}

# Logging configuration
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        },
        'simple': {
            'format': '%(levelname)s - %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'simple'
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'logs/quantumedge.log',
            'level': 'DEBUG',
            'formatter': 'detailed'
        }
    },
    'loggers': {
        'quantumedge': {
            'level': 'DEBUG',
            'handlers': ['console', 'file'],
            'propagate': False
        }
    }
}

# Create necessary directories
import pathlib
pathlib.Path('data').mkdir(exist_ok=True)
pathlib.Path('logs').mkdir(exist_ok=True)
pathlib.Path('cache').mkdir(exist_ok=True)
