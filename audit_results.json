{"timestamp": "2025-07-03T02:16:18.204952", "blueprint_compliance": {"data_engine": {"financial_data": {"description": "yfinance integration (blueprint specifies yfinance)", "implemented": false, "compliance_score": 0.0}, "sentiment_analysis": {"description": "VADER sentiment analyzer (blueprint specifies VADER only)", "implemented": true, "compliance_score": 1.0}, "data_storage": {"description": "SQLite storage (blueprint specifies SQLite)", "implemented": false, "compliance_score": 0.0}, "reddit_scraping": {"description": "Reddit scraping with PRAW (blueprint Week 2)", "implemented": true, "compliance_score": 1.0}, "data_fusion": {"description": "Multi-source data integration", "implemented": true, "compliance_score": 1.0}}, "risk_model": {"volatility_prediction": {"description": "Risk model implementation", "implemented": true, "compliance_score": 1.0}, "portfolio_scoring": {"description": "Portfolio risk scoring", "implemented": true, "compliance_score": 1.0}, "real_time_processing": {"description": "Real-time analysis capability", "implemented": true, "compliance_score": 1.0}}, "interface": {"streamlit_dashboard": {"description": "Web dashboard", "implemented": true, "compliance_score": 1.0}, "user_input": {"description": "Portfolio input capability", "implemented": true, "compliance_score": 1.0}, "risk_display": {"description": "Risk score visualization", "implemented": true, "compliance_score": 1.0}}, "budget_constraints": {"free_apis": {"description": "Zero-cost API usage", "implemented": true, "compliance_score": 1.0}, "m1_max_optimization": {"description": "Local processing on M1 Max", "implemented": true, "compliance_score": 1.0}, "open_source_models": {"description": "No paid model dependencies", "implemented": true, "compliance_score": 1.0}, "scope_limitation": {"description": "50 S&P 500 stocks (blueprint scope)", "implemented": true, "compliance_score": 1.0}}, "overall_score": 0.8666666666666667, "total_requirements": 15, "implemented_count": 13}, "performance_metrics": {"validation_throughput": {"measured": 127325.44867280278, "claimed": 115835, "variance": 0.09919669074807083, "realistic": true}, "data_ingestion": {"processing_time": 0.6126198768615723, "quality_score": 1.0, "realistic": true}, "sentiment_analysis": {"processing_time": 0.8001420497894287, "realistic": true, "data_quality": true}}, "data_quality_assessment": {"validation_accuracy": {"valid_data_passed": true, "invalid_data_rejected": true, "valid_quality_score": 0.96, "invalid_quality_score": 0.5599999999999999, "accuracy_test_passed": true}, "monitoring_system": {"operational": true, "health_data": {"timestamp": "2025-07-03T02:16:19.717643", "metrics": {}, "api_performance": {}, "active_alerts": 0, "alert_summary": {"critical": 0, "high": 0, "medium": 0, "low": 0}}}}, "production_readiness": {"error_handling": {"score": 1.0, "tests_passed": 3, "total_tests": 3}, "api_configuration": {"score": 1.0, "configured_apis": 4, "total_apis": 4}, "security": {"score": 1.0, "checks_passed": 2, "total_checks": 2}, "overall_score": 1.0}, "critical_issues": ["Blueprint compliance below 95% threshold: 86.7%"], "recommendations": ["Complete missing blueprint requirements before production deployment", "Resolve all critical issues before proceeding to live trading", "❌ System does NOT meet 95% confidence threshold - Additional development required"], "final_assessment": {"overall_score": 0.9666666666666667, "component_scores": {"blueprint_compliance": 0.8666666666666667, "performance_realistic": 1.0, "data_quality": 1.0, "production_readiness": 1.0}, "critical_issues_count": 1, "meets_95_percent_threshold": false}}