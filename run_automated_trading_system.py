#!/usr/bin/env python3
"""
QuantumEdge Financial Intelligence Tool - Automated Trading System
Main execution script for automated daily trading with scheduling
"""

import asyncio
import logging
import sys
import os
import signal
from datetime import datetime, time
import schedule
import time as time_module

# Add src to path
sys.path.append('src')

from src.automated_daily_system import automated_system

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('quantumedge_automated.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('quantumedge.automated_main')

class AutomatedTradingRunner:
    """Main runner for automated trading system"""
    
    def __init__(self):
        self.running = False
        self.system_initialized = False
        
    async def initialize_system(self):
        """Initialize the automated trading system"""
        try:
            logger.info("🚀 INITIALIZING QUANTUMEDGE AUTOMATED TRADING SYSTEM")
            logger.info("=" * 80)
            
            # Initialize automated system
            await automated_system.initialize()
            
            self.system_initialized = True
            logger.info("✅ System initialization completed successfully")
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            raise
    
    async def run_daily_cycle(self):
        """Run daily trading cycle"""
        try:
            if not self.system_initialized:
                logger.error("❌ System not initialized, cannot run daily cycle")
                return
            
            logger.info("🌅 STARTING AUTOMATED DAILY TRADING CYCLE")
            logger.info("-" * 50)
            
            # Check if we should run (trading day and time)
            if not automated_system.is_trading_day():
                logger.info("📅 Not a trading day, skipping cycle")
                return
            
            # Execute daily trading cycle
            cycle_results = await automated_system.execute_daily_trading_cycle()
            
            # Log summary
            summary = cycle_results.get('cycle_summary', {})
            logger.info(f"✅ Daily cycle completed:")
            logger.info(f"   Signals Generated: {summary.get('signals_generated', 0)}")
            logger.info(f"   Trades Executed: {summary.get('trades_executed', 0)}")
            logger.info(f"   Successful Trades: {summary.get('successful_trades', 0)}")
            logger.info(f"   Alerts Triggered: {summary.get('alerts_triggered', 0)}")
            
            # Check performance
            performance = cycle_results.get('performance_monitoring', {})
            logger.info(f"📊 Current Performance:")
            logger.info(f"   Total Equity: ${performance.get('total_equity', 0):,.2f}")
            logger.info(f"   Total P&L: ${performance.get('total_pl', 0):,.2f} ({performance.get('total_pl_pct', 0):+.2f}%)")
            logger.info(f"   Win Rate: {performance.get('win_rate', 0):.1f}%")
            
        except Exception as e:
            logger.error(f"❌ Daily cycle failed: {e}")
    
    async def run_performance_dashboard(self):
        """Generate and display performance dashboard"""
        try:
            logger.info("📊 GENERATING PERFORMANCE DASHBOARD")
            logger.info("-" * 50)
            
            dashboard = await automated_system.generate_performance_dashboard()
            
            if 'error' in dashboard:
                logger.warning(f"⚠️ Dashboard generation failed: {dashboard['error']}")
                return
            
            logger.info(f"📈 30-Day Performance Dashboard:")
            logger.info(f"   Period: {dashboard.get('start_date', 'N/A')} to {dashboard.get('end_date', 'N/A')}")
            logger.info(f"   Total Return: {dashboard.get('total_return_pct', 0):+.2f}%")
            logger.info(f"   Avg Daily Return: {dashboard.get('avg_daily_return', 0):+.4f}%")
            logger.info(f"   Sharpe Ratio: {dashboard.get('sharpe_ratio', 0):.2f}")
            logger.info(f"   Max Drawdown: {dashboard.get('max_drawdown', 0):.2f}%")
            logger.info(f"   Win Rate: {dashboard.get('current_win_rate', 0):.1f}%")
            logger.info(f"   Total Trades: {dashboard.get('total_trades', 0)}")
            logger.info(f"   Best Day: {dashboard.get('best_day', 0):+.2f}%")
            logger.info(f"   Worst Day: {dashboard.get('worst_day', 0):+.2f}%")
            
            # Performance assessment
            sharpe = dashboard.get('sharpe_ratio', 0)
            win_rate = dashboard.get('current_win_rate', 0)
            max_dd = dashboard.get('max_drawdown', 0)
            
            logger.info(f"\n🎯 PERFORMANCE ASSESSMENT:")
            
            if sharpe >= 1.0:
                logger.info("✅ Sharpe ratio meets target (≥1.0)")
            else:
                logger.info(f"⚠️ Sharpe ratio below target ({sharpe:.2f} < 1.0)")
            
            if win_rate >= 60:
                logger.info("✅ Win rate meets target (≥60%)")
            else:
                logger.info(f"⚠️ Win rate below target ({win_rate:.1f}% < 60%)")
            
            if max_dd <= 10:
                logger.info("✅ Max drawdown within limit (≤10%)")
            else:
                logger.info(f"⚠️ Max drawdown exceeds limit ({max_dd:.2f}% > 10%)")
            
            # Overall assessment
            targets_met = sum([sharpe >= 1.0, win_rate >= 60, max_dd <= 10])
            
            if targets_met == 3:
                logger.info("🎉 ALL TARGETS MET - Ready for live trading consideration!")
            elif targets_met == 2:
                logger.info("✅ GOOD PERFORMANCE - Minor improvements needed")
            elif targets_met == 1:
                logger.info("⚠️ FAIR PERFORMANCE - Significant improvements needed")
            else:
                logger.info("❌ POOR PERFORMANCE - Strategy requires major revision")
            
        except Exception as e:
            logger.error(f"❌ Performance dashboard failed: {e}")
    
    def schedule_jobs(self):
        """Schedule automated jobs"""
        logger.info("📅 SCHEDULING AUTOMATED JOBS")
        
        # Schedule daily trading cycle at 6:00 AM ET (converted to UTC)
        schedule.every().day.at("11:00").do(self.run_scheduled_job, self.run_daily_cycle)
        
        # Schedule performance dashboard at 5:00 PM ET (converted to UTC)
        schedule.every().day.at("22:00").do(self.run_scheduled_job, self.run_performance_dashboard)
        
        # Schedule weekend performance review on Sundays at 10:00 AM ET
        schedule.every().sunday.at("15:00").do(self.run_scheduled_job, self.run_performance_dashboard)
        
        logger.info("✅ Jobs scheduled:")
        logger.info("   - Daily trading cycle: 6:00 AM ET (11:00 UTC)")
        logger.info("   - Daily dashboard: 5:00 PM ET (22:00 UTC)")
        logger.info("   - Weekly review: Sunday 10:00 AM ET (15:00 UTC)")
    
    def run_scheduled_job(self, job_func):
        """Run scheduled job in async context"""
        try:
            asyncio.run(job_func())
        except Exception as e:
            logger.error(f"❌ Scheduled job failed: {e}")
    
    async def run_manual_cycle(self):
        """Run manual trading cycle for testing"""
        logger.info("🔧 RUNNING MANUAL TRADING CYCLE")
        logger.info("-" * 50)
        
        await self.run_daily_cycle()
        await self.run_performance_dashboard()
    
    def start_scheduler(self):
        """Start the automated scheduler"""
        logger.info("⏰ STARTING AUTOMATED SCHEDULER")
        logger.info("=" * 80)
        
        self.running = True
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        logger.info("🔄 Scheduler running... Press Ctrl+C to stop")
        
        try:
            while self.running:
                schedule.run_pending()
                time_module.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            logger.info("⏹️ Scheduler stopped by user")
        except Exception as e:
            logger.error(f"❌ Scheduler error: {e}")
        finally:
            self.running = False
            logger.info("🛑 Automated scheduler stopped")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"📡 Received signal {signum}, shutting down gracefully...")
        self.running = False

async def main():
    """Main execution function"""
    runner = AutomatedTradingRunner()
    
    try:
        # Initialize system
        await runner.initialize_system()
        
        # Check command line arguments
        if len(sys.argv) > 1:
            command = sys.argv[1].lower()
            
            if command == "manual":
                # Run manual cycle for testing
                await runner.run_manual_cycle()
                
            elif command == "dashboard":
                # Show performance dashboard only
                await runner.run_performance_dashboard()
                
            elif command == "cycle":
                # Run single trading cycle
                await runner.run_daily_cycle()
                
            elif command == "schedule":
                # Start automated scheduler
                runner.schedule_jobs()
                runner.start_scheduler()
                
            else:
                logger.error(f"❌ Unknown command: {command}")
                print_usage()
        else:
            # Default: run manual cycle
            logger.info("🔧 No command specified, running manual cycle")
            await runner.run_manual_cycle()
            
    except KeyboardInterrupt:
        logger.info("⏹️ Execution interrupted by user")
    except Exception as e:
        logger.error(f"❌ Execution failed: {e}")
        raise

def print_usage():
    """Print usage information"""
    print("""
QuantumEdge Automated Trading System

Usage:
    python run_automated_trading_system.py [command]

Commands:
    manual      Run manual trading cycle (default)
    dashboard   Show performance dashboard only
    cycle       Run single trading cycle
    schedule    Start automated scheduler (runs continuously)

Examples:
    python run_automated_trading_system.py manual
    python run_automated_trading_system.py schedule
    python run_automated_trading_system.py dashboard
""")

if __name__ == "__main__":
    # Run the automated trading system
    asyncio.run(main())
