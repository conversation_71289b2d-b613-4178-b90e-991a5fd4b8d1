"""
QuantumEdge Financial Intelligence Tool - News & Sentiment Analysis Engine
Advanced sentiment analysis using VADER and multi-source data integration
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import aiohttp
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
from dataclasses import dataclass
import re
import json

from src.config import config
from src.rate_limiter import rate_limiter
from src.data_validation import DataValidator

logger = logging.getLogger('quantumedge.sentiment')

@dataclass
class SentimentResult:
    """Sentiment analysis result"""
    text: str
    sentiment_score: float  # -1 (negative) to 1 (positive)
    confidence: float
    sentiment_label: str  # 'positive', 'negative', 'neutral'
    source: str
    timestamp: datetime
    symbol: Optional[str] = None
    
    def to_dict(self) -> Dict:
        return {
            'text': self.text,
            'sentiment_score': self.sentiment_score,
            'confidence': self.confidence,
            'sentiment_label': self.sentiment_label,
            'source': self.source,
            'timestamp': self.timestamp.isoformat(),
            'symbol': self.symbol
        }

@dataclass
class NewsArticle:
    """News article data structure"""
    headline: str
    summary: str
    url: str
    published_at: datetime
    source: str
    symbol: Optional[str] = None
    
    def to_dict(self) -> Dict:
        return {
            'headline': self.headline,
            'summary': self.summary,
            'url': self.url,
            'published_at': self.published_at.isoformat(),
            'source': self.source,
            'symbol': self.symbol
        }

class VADERSentimentAnalyzer:
    """VADER-based sentiment analyzer optimized for financial text"""

    def __init__(self):
        self.analyzer = SentimentIntensityAnalyzer()

        # Financial-specific lexicon adjustments
        self.financial_lexicon = {
            'bullish': 2.0, 'bearish': -2.0, 'rally': 1.5, 'crash': -2.5,
            'surge': 1.8, 'plunge': -2.0, 'soar': 2.0, 'tumble': -1.8,
            'outperform': 1.5, 'underperform': -1.5, 'beat': 1.2, 'miss': -1.2,
            'upgrade': 1.3, 'downgrade': -1.3, 'buy': 1.0, 'sell': -1.0,
            'strong': 1.2, 'weak': -1.2, 'growth': 1.0, 'decline': -1.0
        }

        # Update VADER lexicon with financial terms
        self.analyzer.lexicon.update(self.financial_lexicon)

    def analyze_sentiment(self, text: str) -> Tuple[float, float, str]:
        """
        Analyze sentiment of financial text using enhanced VADER

        Returns:
            Tuple of (sentiment_score, confidence, label)
        """
        if not text or len(text.strip()) == 0:
            return 0.0, 0.0, 'neutral'

        try:
            scores = self.analyzer.polarity_scores(text)

            sentiment_score = scores['compound']
            confidence = max(abs(scores['pos']), abs(scores['neg']), abs(scores['neu']))

            if sentiment_score >= 0.1:
                label = 'positive'
            elif sentiment_score <= -0.1:
                label = 'negative'
            else:
                label = 'neutral'

            return sentiment_score, confidence, label

        except Exception as e:
            logger.warning(f"VADER analysis failed: {e}")
            return 0.0, 0.0, 'neutral'

class NewsDataClient:
    """News data client with multiple sources"""
    
    def __init__(self):
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_alpha_vantage_news(self, symbol: str, limit: int = 50) -> List[NewsArticle]:
        """Get news from Alpha Vantage"""
        if not config.apis['alpha_vantage'].api_key:
            return []
        
        can_proceed, rate_info = await rate_limiter.acquire('alpha_vantage')
        if not can_proceed:
            logger.warning("Rate limited for Alpha Vantage news")
            return []
        
        url = f"{config.apis['alpha_vantage'].base_url}/query"
        params = {
            'function': 'NEWS_SENTIMENT',
            'tickers': symbol,
            'limit': limit,
            'apikey': config.apis['alpha_vantage'].api_key
        }
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_alpha_vantage_news(data, symbol)
                else:
                    logger.error(f"Alpha Vantage news error {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Error fetching Alpha Vantage news: {e}")
            return []
    
    def _parse_alpha_vantage_news(self, data: Dict, symbol: str) -> List[NewsArticle]:
        """Parse Alpha Vantage news response"""
        articles = []
        feed = data.get('feed', [])
        
        for item in feed:
            try:
                articles.append(NewsArticle(
                    headline=item.get('title', ''),
                    summary=item.get('summary', ''),
                    url=item.get('url', ''),
                    published_at=datetime.strptime(
                        item.get('time_published', ''), '%Y%m%dT%H%M%S'
                    ),
                    source='alpha_vantage',
                    symbol=symbol
                ))
            except Exception as e:
                logger.warning(f"Error parsing Alpha Vantage news item: {e}")
                continue
        
        return articles

class RedditSentimentClient:
    """Reddit sentiment analysis client"""
    
    def __init__(self):
        self.reddit = None
        
    async def initialize(self):
        """Initialize Reddit client"""
        try:
            # Note: This requires Reddit API credentials
            # For demo purposes, we'll use a mock implementation
            logger.info("Reddit client initialized (mock mode)")
            
        except Exception as e:
            logger.warning(f"Reddit initialization failed: {e}")
    
    async def get_subreddit_sentiment(self, symbol: str, subreddit: str = 'investing', 
                                    limit: int = 100) -> List[Dict]:
        """Get sentiment from Reddit posts"""
        # Mock implementation for demo
        # In production, this would use PRAW to fetch real Reddit data
        
        mock_posts = [
            {
                'title': f'{symbol} earnings beat expectations',
                'selftext': f'Great quarter for {symbol}, revenue up 15%',
                'created_utc': datetime.utcnow().timestamp(),
                'score': 25,
                'num_comments': 12
            },
            {
                'title': f'Thoughts on {symbol} long term?',
                'selftext': f'Considering a position in {symbol}, what do you think?',
                'created_utc': datetime.utcnow().timestamp(),
                'score': 8,
                'num_comments': 5
            }
        ]
        
        return mock_posts

class SentimentEngine:
    """Main sentiment analysis engine"""
    
    def __init__(self):
        self.sentiment_analyzer = VADERSentimentAnalyzer()
        self.news_client = None
        self.reddit_client = RedditSentimentClient()
        self.validator = DataValidator()

    async def initialize(self):
        """Initialize all components"""
        await self.reddit_client.initialize()
        logger.info("Sentiment engine initialized with VADER analyzer")
    
    async def analyze_symbol_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Comprehensive sentiment analysis for a symbol"""
        results = {
            'symbol': symbol,
            'timestamp': datetime.utcnow().isoformat(),
            'news_sentiment': [],
            'social_sentiment': [],
            'overall_sentiment': {
                'score': 0.0,
                'confidence': 0.0,
                'label': 'neutral'
            }
        }
        
        # Get news sentiment
        async with NewsDataClient() as news_client:
            news_articles = await news_client.get_alpha_vantage_news(symbol)
            
            for article in news_articles:
                # Analyze headline sentiment
                headline_sentiment = await self._analyze_text_sentiment(
                    article.headline, 'news', symbol
                )
                
                # Analyze summary sentiment
                summary_sentiment = await self._analyze_text_sentiment(
                    article.summary, 'news', symbol
                )
                
                # Combine headline and summary
                combined_score = (headline_sentiment.sentiment_score + 
                                summary_sentiment.sentiment_score) / 2
                combined_confidence = (headline_sentiment.confidence + 
                                     summary_sentiment.confidence) / 2
                
                article_sentiment = SentimentResult(
                    text=f"{article.headline} {article.summary}",
                    sentiment_score=combined_score,
                    confidence=combined_confidence,
                    sentiment_label=self._score_to_label(combined_score),
                    source='news',
                    timestamp=article.published_at,
                    symbol=symbol
                )
                
                results['news_sentiment'].append(article_sentiment.to_dict())
        
        # Get social media sentiment
        reddit_posts = await self.reddit_client.get_subreddit_sentiment(symbol)
        
        for post in reddit_posts:
            text = f"{post['title']} {post.get('selftext', '')}"
            social_sentiment = await self._analyze_text_sentiment(
                text, 'reddit', symbol
            )
            results['social_sentiment'].append(social_sentiment.to_dict())
        
        # Calculate overall sentiment
        all_sentiments = (results['news_sentiment'] + results['social_sentiment'])
        if all_sentiments:
            avg_score = sum(s['sentiment_score'] for s in all_sentiments) / len(all_sentiments)
            avg_confidence = sum(s['confidence'] for s in all_sentiments) / len(all_sentiments)
            
            results['overall_sentiment'] = {
                'score': avg_score,
                'confidence': avg_confidence,
                'label': self._score_to_label(avg_score),
                'sample_size': len(all_sentiments)
            }
        
        return results
    
    async def _analyze_text_sentiment(self, text: str, source: str, 
                                    symbol: Optional[str] = None) -> SentimentResult:
        """Analyze sentiment of a single text"""
        if not text or len(text.strip()) == 0:
            return SentimentResult(
                text='',
                sentiment_score=0.0,
                confidence=0.0,
                sentiment_label='neutral',
                source=source,
                timestamp=datetime.utcnow(),
                symbol=symbol
            )
        
        # Clean text
        cleaned_text = self._clean_text(text)
        
        # Analyze with VADER
        sentiment_score, confidence, label = self.sentiment_analyzer.analyze_sentiment(cleaned_text)
        
        return SentimentResult(
            text=cleaned_text,
            sentiment_score=sentiment_score,
            confidence=confidence,
            sentiment_label=label,
            source=source,
            timestamp=datetime.utcnow(),
            symbol=symbol
        )
    
    def _clean_text(self, text: str) -> str:
        """Clean and preprocess text for sentiment analysis"""
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s.,!?-]', '', text)
        
        return text
    
    def _score_to_label(self, score: float) -> str:
        """Convert sentiment score to label"""
        if score >= 0.1:
            return 'positive'
        elif score <= -0.1:
            return 'negative'
        else:
            return 'neutral'
    
    async def get_sentiment_summary(self, symbols: List[str]) -> Dict[str, Any]:
        """Get sentiment summary for multiple symbols"""
        summary = {
            'timestamp': datetime.utcnow().isoformat(),
            'symbols_analyzed': len(symbols),
            'sentiment_distribution': {'positive': 0, 'negative': 0, 'neutral': 0},
            'average_sentiment': 0.0,
            'symbol_sentiments': {}
        }
        
        total_score = 0.0
        
        for symbol in symbols:
            try:
                sentiment_data = await self.analyze_symbol_sentiment(symbol)
                overall = sentiment_data['overall_sentiment']
                
                summary['symbol_sentiments'][symbol] = overall
                summary['sentiment_distribution'][overall['label']] += 1
                total_score += overall['score']
                
            except Exception as e:
                logger.error(f"Error analyzing sentiment for {symbol}: {e}")
                continue
        
        if symbols:
            summary['average_sentiment'] = total_score / len(symbols)
        
        return summary

# Global sentiment engine instance
sentiment_engine = SentimentEngine()

async def initialize_sentiment_engine():
    """Initialize the global sentiment engine"""
    await sentiment_engine.initialize()
