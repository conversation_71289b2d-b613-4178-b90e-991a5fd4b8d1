#!/usr/bin/env python3
"""
QuantumEdge Financial Intelligence Tool - Real Paper Trading & Backtesting Demo
Tests real Alpaca connection and runs historical backtesting with actual market data
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')

from src.alpaca_trading_client import alpaca_client
from src.real_backtesting_engine import backtest_engine
from src.live_paper_trading import live_trading_system

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('quantumedge.real_demo')

async def test_real_alpaca_connection():
    """Test real Alpaca API connection with proper error handling"""
    logger.info("🔍 TESTING REAL ALPACA CONNECTION")
    logger.info("-" * 50)
    
    try:
        # Initialize Alpaca client
        await alpaca_client.initialize()
        
        # Get account information
        account_info = await alpaca_client.get_account_info()
        
        if 'error' in account_info:
            logger.error(f"❌ Connection failed: {account_info['error']}")
            logger.error("🔧 TROUBLESHOOTING STEPS:")
            logger.error("1. Verify you're using PAPER TRADING API keys")
            logger.error("2. Generate new API keys from your Alpaca paper account")
            logger.error("3. Update .env file with paper trading credentials")
            logger.error("4. Ensure account is active and not restricted")
            return False
        
        # Display account details
        logger.info("✅ Successfully connected to Alpaca!")
        logger.info(f"📊 Account Type: {'Paper Trading' if 'PA' in account_info['account_number'] else 'Live Trading'}")
        logger.info(f"📊 Account Number: {account_info['account_number']}")
        logger.info(f"💰 Total Equity: ${account_info['equity']:,.2f}")
        logger.info(f"💵 Available Cash: ${account_info['cash']:,.2f}")
        logger.info(f"🔄 Buying Power: ${account_info['buying_power']:,.2f}")
        logger.info(f"📈 Portfolio Value: ${account_info['portfolio_value']:,.2f}")
        
        # Check for existing positions
        if account_info.get('positions'):
            logger.info(f"📊 Current Positions: {len(account_info['positions'])}")
            for pos in account_info['positions']:
                pnl_indicator = "📈" if pos['unrealized_pl'] >= 0 else "📉"
                logger.info(f"   {pnl_indicator} {pos['symbol']}: {pos['qty']} shares, "
                           f"${pos['market_value']:,.2f} value, "
                           f"${pos['unrealized_pl']:,.2f} P&L")
        else:
            logger.info("📊 No current positions (clean slate for testing)")
        
        # Verify this is paper trading
        if account_info['equity'] == 100000.0:
            logger.info("✅ Confirmed: Paper trading account with $100K virtual capital")
        else:
            logger.warning("⚠️ Account equity is not $100K - verify this is paper trading")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Connection test failed: {e}")
        logger.error("🔧 Check your .env file and ensure Alpaca credentials are correct")
        return False

async def test_historical_data_access():
    """Test access to historical market data via Alpaca"""
    logger.info("\n📈 TESTING HISTORICAL DATA ACCESS")
    logger.info("-" * 50)
    
    try:
        # Test getting historical data for AAPL
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=30)
        
        logger.info(f"📊 Fetching 30 days of AAPL data...")
        historical_data = await backtest_engine.get_historical_data('AAPL', start_date, end_date)
        
        if historical_data.empty:
            logger.error("❌ No historical data retrieved")
            return False
        
        logger.info(f"✅ Retrieved {len(historical_data)} days of data")
        logger.info(f"📅 Date range: {historical_data['timestamp'].min().date()} to {historical_data['timestamp'].max().date()}")
        
        # Show sample data
        latest = historical_data.iloc[-1]
        logger.info(f"📊 Latest AAPL data:")
        logger.info(f"   Date: {latest['timestamp'].date()}")
        logger.info(f"   Open: ${latest['open']:.2f}")
        logger.info(f"   High: ${latest['high']:.2f}")
        logger.info(f"   Low: ${latest['low']:.2f}")
        logger.info(f"   Close: ${latest['close']:.2f}")
        logger.info(f"   Volume: {latest['volume']:,}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Historical data test failed: {e}")
        return False

async def run_real_backtest():
    """Run a real backtest using historical data"""
    logger.info("\n🔄 RUNNING REAL HISTORICAL BACKTEST")
    logger.info("-" * 50)
    
    try:
        # Define backtest parameters
        symbols = ['AAPL', 'MSFT', 'GOOGL']  # Start with 3 liquid stocks
        end_date = datetime.utcnow() - timedelta(days=1)  # Yesterday
        start_date = end_date - timedelta(days=90)  # 3 months back
        
        logger.info(f"📊 Backtest Parameters:")
        logger.info(f"   Symbols: {symbols}")
        logger.info(f"   Period: {start_date.date()} to {end_date.date()}")
        logger.info(f"   Initial Capital: ${backtest_engine.initial_capital:,.2f}")
        logger.info(f"   Max Position Size: ${backtest_engine.max_position_size:.2f}")
        logger.info(f"   Stop Loss: {backtest_engine.stop_loss_pct*100:.1f}%")
        logger.info(f"   Take Profit: {backtest_engine.take_profit_pct*100:.1f}%")
        
        # Run backtest
        logger.info("\n🚀 Starting backtest execution...")
        results = await backtest_engine.run_backtest(symbols, start_date, end_date)
        
        # Display results
        logger.info("\n📊 BACKTEST RESULTS")
        logger.info("=" * 50)
        logger.info(f"💰 Performance Summary:")
        logger.info(f"   Initial Capital: ${results.initial_capital:,.2f}")
        logger.info(f"   Final Capital: ${results.final_capital:,.2f}")
        logger.info(f"   Total Return: ${results.total_return:,.2f} ({results.total_return_pct:+.2f}%)")
        logger.info(f"   Sharpe Ratio: {results.sharpe_ratio:.2f}")
        logger.info(f"   Max Drawdown: {results.max_drawdown:.2f}%")
        
        logger.info(f"\n📈 Trading Statistics:")
        logger.info(f"   Total Trades: {results.total_trades}")
        logger.info(f"   Winning Trades: {results.winning_trades}")
        logger.info(f"   Losing Trades: {results.losing_trades}")
        logger.info(f"   Win Rate: {results.win_rate:.1f}%")
        logger.info(f"   Average Win: ${results.avg_win:.2f}")
        logger.info(f"   Average Loss: ${results.avg_loss:.2f}")
        
        # Show sample trades
        if results.trades:
            logger.info(f"\n🔍 Sample Trades (last 5):")
            for trade in results.trades[-5:]:
                pnl_indicator = "📈" if trade.pnl and trade.pnl > 0 else "📉"
                logger.info(f"   {pnl_indicator} {trade.symbol}: {trade.entry_date.date()} -> "
                           f"{trade.exit_date.date() if trade.exit_date else 'Open'}, "
                           f"${trade.entry_price:.2f} -> ${trade.exit_price:.2f if trade.exit_price else 'N/A'}, "
                           f"P&L: ${trade.pnl:.2f if trade.pnl else 'N/A'}")
        
        # Save results
        results_file = f"backtest_results_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(results.to_dict(), f, indent=2, default=str)
        
        logger.info(f"\n📄 Results saved to: {results_file}")
        
        # Performance assessment
        logger.info(f"\n🎯 PERFORMANCE ASSESSMENT:")
        
        if results.total_return_pct > 0:
            logger.info("✅ Strategy was profitable over backtest period")
        else:
            logger.info("❌ Strategy was unprofitable over backtest period")
        
        if results.win_rate >= 60:
            logger.info("✅ Win rate meets 60% target")
        else:
            logger.info(f"⚠️ Win rate ({results.win_rate:.1f}%) below 60% target")
        
        if results.sharpe_ratio >= 1.0:
            logger.info("✅ Sharpe ratio meets 1.0+ target")
        else:
            logger.info(f"⚠️ Sharpe ratio ({results.sharpe_ratio:.2f}) below 1.0 target")
        
        if results.max_drawdown <= 10:
            logger.info("✅ Max drawdown within 10% limit")
        else:
            logger.info(f"⚠️ Max drawdown ({results.max_drawdown:.2f}%) exceeds 10% limit")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Backtest failed: {e}")
        return None

async def test_live_paper_trading():
    """Test live paper trading functionality"""
    logger.info("\n🚀 TESTING LIVE PAPER TRADING")
    logger.info("-" * 50)
    
    try:
        # Initialize live trading system
        await live_trading_system.initialize()
        
        # Run pre-market analysis
        logger.info("📊 Running pre-market analysis...")
        analysis_results = await live_trading_system.run_pre_market_analysis()
        
        if 'error' in analysis_results:
            logger.error(f"❌ Analysis failed: {analysis_results['error']}")
            return False
        
        # Display analysis results
        signals = analysis_results.get('trading_signals', [])
        logger.info(f"✅ Analysis completed, generated {len(signals)} signals")
        
        for signal in signals:
            logger.info(f"🎯 Signal: {signal['action']} {signal['symbol']} "
                       f"(confidence: {signal['confidence']:.3f})")
        
        # Test trade execution (if signals exist)
        if signals:
            logger.info("\n🚀 Testing trade execution...")
            executions = await live_trading_system.execute_trading_signals()
            
            for execution in executions:
                if execution.status == 'SUBMITTED':
                    logger.info(f"✅ Trade submitted: {execution.signal.symbol} "
                               f"(Order ID: {execution.order_id})")
                else:
                    logger.warning(f"⚠️ Trade failed: {execution.signal.symbol} - "
                                 f"{execution.error_message}")
        else:
            logger.info("📊 No trading signals generated (market conditions not met)")
        
        # Get portfolio performance
        performance = await live_trading_system.monitor_positions()
        
        if 'error' not in performance:
            portfolio = performance['portfolio_performance']
            logger.info(f"\n📈 Current Portfolio:")
            logger.info(f"   Total Equity: ${portfolio['total_equity']:,.2f}")
            logger.info(f"   Total P&L: ${portfolio['total_pl']:,.2f}")
            logger.info(f"   Active Positions: {portfolio['total_positions']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Live paper trading test failed: {e}")
        return False

async def main():
    """Main demo function"""
    logger.info("🚀 QUANTUMEDGE REAL PAPER TRADING & BACKTESTING DEMO")
    logger.info("=" * 80)
    
    # Test results tracking
    test_results = {
        'alpaca_connection': False,
        'historical_data': False,
        'backtesting': False,
        'live_trading': False
    }
    
    try:
        # 1. Test Alpaca connection
        logger.info("🔧 PHASE 1: ALPACA CONNECTION TEST")
        test_results['alpaca_connection'] = await test_real_alpaca_connection()
        
        if not test_results['alpaca_connection']:
            logger.error("❌ Cannot proceed without Alpaca connection")
            logger.error("🔧 Please fix Alpaca credentials and try again")
            return
        
        # 2. Test historical data access
        logger.info("\n🔧 PHASE 2: HISTORICAL DATA TEST")
        test_results['historical_data'] = await test_historical_data_access()
        
        if not test_results['historical_data']:
            logger.error("❌ Cannot run backtests without historical data")
            return
        
        # 3. Run real backtest
        logger.info("\n🔧 PHASE 3: REAL BACKTESTING")
        backtest_results = await run_real_backtest()
        test_results['backtesting'] = backtest_results is not None
        
        # 4. Test live paper trading
        logger.info("\n🔧 PHASE 4: LIVE PAPER TRADING TEST")
        test_results['live_trading'] = await test_live_paper_trading()
        
        # Final summary
        logger.info("\n🎉 DEMO COMPLETION SUMMARY")
        logger.info("=" * 80)
        
        for test_name, result in test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
        
        all_passed = all(test_results.values())
        
        if all_passed:
            logger.info("\n🎯 ALL TESTS PASSED - SYSTEM READY FOR DEPLOYMENT")
            logger.info("📊 Next steps:")
            logger.info("1. Review backtest results and adjust parameters if needed")
            logger.info("2. Start daily paper trading with small position sizes")
            logger.info("3. Monitor performance for 2 weeks before scaling")
            logger.info("4. Maintain detailed logs and performance tracking")
        else:
            logger.info("\n⚠️ SOME TESTS FAILED - REVIEW AND FIX ISSUES")
            logger.info("🔧 Address failed components before live deployment")
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ Demo interrupted by user")
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        raise

if __name__ == "__main__":
    # Run the real paper trading demo
    asyncio.run(main())
