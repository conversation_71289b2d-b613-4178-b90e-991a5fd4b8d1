{"tests/test_sentiment_engine.py::TestVADERSentimentAnalyzer": true, "tests/test_sentiment_engine.py::TestNewsDataClient": true, "tests/test_sentiment_engine.py::TestRedditSentimentClient": true, "tests/test_sentiment_engine.py::TestSentimentEngine": true, "tests/test_sentiment_engine.py::TestSentimentResult": true, "tests/test_sentiment_engine.py::TestIntegration": true, "tests/test_monitoring_dashboard.py::TestDataQualityMonitor::test_low_quality_alert_creation": true, "tests/test_monitoring_dashboard.py::TestDataQualityMonitor::test_system_health_retrieval": true, "tests/test_monitoring_dashboard.py::TestDataQualityMonitor::test_data_quality_report": true, "tests/test_monitoring_dashboard.py::TestIntegration::test_end_to_end_monitoring": true, "tests/test_monitoring_dashboard.py::TestIntegration::test_performance_requirements": true}