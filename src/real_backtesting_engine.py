"""
QuantumEdge Financial Intelligence Tool - Real Backtesting Engine
Historical backtesting with real market data and live paper trading validation
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
from dataclasses import dataclass

from src.alpaca_trading_client import alpaca_client, TradingSignal
from src.market_data_pipeline import MarketDataPipeline
from src.sentiment_engine import sentiment_engine
from src.risk_analytics import risk_analyzer
from src.alternative_data import alternative_data_engine

logger = logging.getLogger('quantumedge.backtesting')

@dataclass
class BacktestTrade:
    """Individual backtest trade record"""
    entry_date: datetime
    exit_date: Optional[datetime]
    symbol: str
    action: str  # 'BUY' or 'SELL'
    entry_price: float
    exit_price: Optional[float]
    quantity: float
    pnl: Optional[float]
    pnl_pct: Optional[float]
    confidence: float
    sentiment_score: float
    risk_score: float
    regime: str
    
    def to_dict(self) -> Dict:
        return {
            'entry_date': self.entry_date.isoformat(),
            'exit_date': self.exit_date.isoformat() if self.exit_date else None,
            'symbol': self.symbol,
            'action': self.action,
            'entry_price': self.entry_price,
            'exit_price': self.exit_price,
            'quantity': self.quantity,
            'pnl': self.pnl,
            'pnl_pct': self.pnl_pct,
            'confidence': self.confidence,
            'sentiment_score': self.sentiment_score,
            'risk_score': self.risk_score,
            'regime': self.regime
        }

@dataclass
class BacktestResults:
    """Comprehensive backtest results"""
    start_date: datetime
    end_date: datetime
    initial_capital: float
    final_capital: float
    total_return: float
    total_return_pct: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    trades: List[BacktestTrade]
    
    def to_dict(self) -> Dict:
        return {
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat(),
            'initial_capital': self.initial_capital,
            'final_capital': self.final_capital,
            'total_return': self.total_return,
            'total_return_pct': self.total_return_pct,
            'sharpe_ratio': self.sharpe_ratio,
            'max_drawdown': self.max_drawdown,
            'win_rate': self.win_rate,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'avg_win': self.avg_win,
            'avg_loss': self.avg_loss,
            'trades': [trade.to_dict() for trade in self.trades]
        }

class RealBacktestingEngine:
    """
    Real backtesting engine using historical market data and QuantumEdge analysis
    Validates strategy performance before live paper trading deployment
    """
    
    def __init__(self):
        self.initial_capital = 10000.0  # $10K starting capital
        self.max_position_size = 500.0  # $500 max per position
        self.stop_loss_pct = 0.02  # 2% stop loss
        self.take_profit_pct = 0.04  # 4% take profit
        self.commission = 0.0  # Alpaca commission-free
        
        # Strategy parameters
        self.min_confidence = 0.7
        self.min_sentiment = 0.6
        self.max_risk = 0.05
        self.min_regime_confidence = 0.7
        
        # Tracking
        self.trades = []
        self.portfolio_value = []
        self.daily_returns = []
        
    async def get_historical_data(self, symbol: str, start_date: datetime, 
                                end_date: datetime) -> pd.DataFrame:
        """Get historical market data for backtesting"""
        try:
            # Use Alpaca data client for historical data
            from alpaca.data.requests import StockBarsRequest
            from alpaca.data.timeframe import TimeFrame
            
            request = StockBarsRequest(
                symbol_or_symbols=[symbol],
                timeframe=TimeFrame.Day,
                start=start_date,
                end=end_date
            )
            
            bars = alpaca_client.data_client.get_stock_bars(request)
            
            if symbol in bars:
                data = []
                for bar in bars[symbol]:
                    data.append({
                        'timestamp': bar.timestamp,
                        'open': float(bar.open),
                        'high': float(bar.high),
                        'low': float(bar.low),
                        'close': float(bar.close),
                        'volume': int(bar.volume)
                    })
                
                df = pd.DataFrame(data)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.sort_values('timestamp')
                return df
            
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return pd.DataFrame()
    
    async def analyze_historical_signal(self, symbol: str, date: datetime, 
                                      historical_data: pd.DataFrame) -> Optional[TradingSignal]:
        """
        Generate trading signal for historical date using QuantumEdge analysis
        Uses only data available up to that date (no look-ahead bias)
        """
        try:
            # Get data up to the analysis date (no look-ahead bias)
            available_data = historical_data[historical_data['timestamp'] <= date].copy()
            
            if len(available_data) < 30:  # Need minimum data for analysis
                return None
            
            # Calculate returns for risk analysis
            available_data['returns'] = available_data['close'].pct_change()
            returns = available_data['returns'].dropna().values
            
            if len(returns) < 20:
                return None
            
            # Risk analysis using historical data
            var_95 = np.percentile(returns, 5)  # 95% VaR
            volatility = np.std(returns) * np.sqrt(252)  # Annualized volatility
            risk_score = abs(var_95)
            
            # Simplified sentiment analysis (in real backtest, this would use historical news)
            # For now, use price momentum as proxy
            recent_returns = returns[-5:]  # Last 5 days
            momentum = np.mean(recent_returns)
            sentiment_score = max(0, min(1, 0.5 + momentum * 10))  # Scale to 0-1
            
            # Market regime detection (simplified)
            long_term_vol = np.std(returns[-60:]) if len(returns) >= 60 else np.std(returns)
            short_term_vol = np.std(returns[-20:]) if len(returns) >= 20 else np.std(returns)
            
            if short_term_vol > long_term_vol * 1.5:
                regime = "High Volatility"
                regime_confidence = 0.8
            elif short_term_vol < long_term_vol * 0.7:
                regime = "Low Volatility"
                regime_confidence = 0.8
            else:
                regime = "Normal"
                regime_confidence = 0.6
            
            # Check signal criteria
            if sentiment_score < self.min_sentiment:
                return None
            
            if risk_score > self.max_risk:
                return None
            
            if regime_confidence < self.min_regime_confidence:
                return None
            
            # Calculate overall confidence
            confidence = (sentiment_score * 0.4 + (1 - risk_score) * 0.3 + regime_confidence * 0.3)
            
            if confidence < self.min_confidence:
                return None
            
            # Generate signal
            signal = TradingSignal(
                symbol=symbol,
                action='BUY',  # Only long positions for now
                confidence=confidence,
                sentiment_score=sentiment_score,
                risk_score=risk_score,
                regime_confidence=regime_confidence,
                position_size=min(self.max_position_size, confidence * self.max_position_size)
            )
            
            return signal
            
        except Exception as e:
            logger.error(f"Error analyzing historical signal for {symbol} on {date}: {e}")
            return None
    
    async def simulate_trade_execution(self, signal: TradingSignal, date: datetime,
                                     price_data: pd.DataFrame) -> Optional[BacktestTrade]:
        """Simulate trade execution with realistic assumptions"""
        try:
            # Get price for entry date
            entry_data = price_data[price_data['timestamp'].dt.date == date.date()]
            
            if entry_data.empty:
                return None
            
            entry_price = entry_data.iloc[0]['open']  # Enter at next day's open
            quantity = signal.position_size / entry_price
            
            # Find exit conditions
            exit_date = None
            exit_price = None
            
            # Look for exit conditions in subsequent days
            future_data = price_data[price_data['timestamp'] > date].copy()
            
            for idx, row in future_data.iterrows():
                current_price = row['close']
                
                # Check stop loss
                if current_price <= entry_price * (1 - self.stop_loss_pct):
                    exit_date = row['timestamp']
                    exit_price = current_price
                    break
                
                # Check take profit
                if current_price >= entry_price * (1 + self.take_profit_pct):
                    exit_date = row['timestamp']
                    exit_price = current_price
                    break
                
                # Maximum holding period (30 days)
                if (row['timestamp'] - date).days >= 30:
                    exit_date = row['timestamp']
                    exit_price = current_price
                    break
            
            # If no exit condition met, use last available price
            if exit_date is None and not future_data.empty:
                last_row = future_data.iloc[-1]
                exit_date = last_row['timestamp']
                exit_price = last_row['close']
            
            # Calculate P&L
            if exit_price is not None:
                pnl = (exit_price - entry_price) * quantity
                pnl_pct = (exit_price - entry_price) / entry_price
            else:
                pnl = None
                pnl_pct = None
            
            trade = BacktestTrade(
                entry_date=date,
                exit_date=exit_date,
                symbol=signal.symbol,
                action=signal.action,
                entry_price=entry_price,
                exit_price=exit_price,
                quantity=quantity,
                pnl=pnl,
                pnl_pct=pnl_pct,
                confidence=signal.confidence,
                sentiment_score=signal.sentiment_score,
                risk_score=signal.risk_score,
                regime="Historical"
            )
            
            return trade
            
        except Exception as e:
            logger.error(f"Error simulating trade execution: {e}")
            return None
    
    async def run_backtest(self, symbols: List[str], start_date: datetime, 
                         end_date: datetime) -> BacktestResults:
        """
        Run comprehensive backtest on historical data
        
        Args:
            symbols: List of symbols to backtest
            start_date: Backtest start date
            end_date: Backtest end date
            
        Returns:
            BacktestResults with comprehensive performance metrics
        """
        logger.info(f"🔄 Starting backtest: {start_date.date()} to {end_date.date()}")
        logger.info(f"📊 Symbols: {symbols}")
        
        # Initialize tracking
        self.trades = []
        current_capital = self.initial_capital
        portfolio_values = [current_capital]
        dates = []
        
        try:
            # Get historical data for all symbols
            historical_data = {}
            for symbol in symbols:
                logger.info(f"📈 Loading historical data for {symbol}...")
                data = await self.get_historical_data(symbol, start_date, end_date)
                if not data.empty:
                    historical_data[symbol] = data
                    logger.info(f"✅ Loaded {len(data)} days of data for {symbol}")
                else:
                    logger.warning(f"⚠️ No data available for {symbol}")
            
            # Generate trading dates (business days only)
            current_date = start_date
            while current_date <= end_date:
                if current_date.weekday() < 5:  # Monday = 0, Friday = 4
                    dates.append(current_date)
                current_date += timedelta(days=1)
            
            logger.info(f"📅 Backtesting {len(dates)} trading days")
            
            # Run backtest day by day
            for i, date in enumerate(dates):
                if i % 50 == 0:  # Progress update every 50 days
                    logger.info(f"📊 Progress: {i}/{len(dates)} days ({i/len(dates)*100:.1f}%)")
                
                # Analyze each symbol for signals
                for symbol in symbols:
                    if symbol not in historical_data:
                        continue
                    
                    # Generate signal for this date
                    signal = await self.analyze_historical_signal(
                        symbol, date, historical_data[symbol]
                    )
                    
                    if signal is None:
                        continue
                    
                    # Check if we have enough capital
                    if current_capital < signal.position_size:
                        continue
                    
                    # Simulate trade execution
                    trade = await self.simulate_trade_execution(
                        signal, date, historical_data[symbol]
                    )
                    
                    if trade is not None:
                        self.trades.append(trade)
                        
                        # Update capital when trade exits
                        if trade.pnl is not None:
                            current_capital += trade.pnl
                            logger.debug(f"💰 Trade completed: {symbol} P&L: ${trade.pnl:.2f}")
                
                # Track portfolio value
                portfolio_values.append(current_capital)
            
            # Calculate performance metrics
            results = self._calculate_performance_metrics(
                start_date, end_date, portfolio_values
            )
            
            logger.info(f"✅ Backtest completed!")
            logger.info(f"📊 Total trades: {results.total_trades}")
            logger.info(f"💰 Total return: {results.total_return_pct:.2f}%")
            logger.info(f"📈 Win rate: {results.win_rate:.1f}%")
            logger.info(f"📉 Max drawdown: {results.max_drawdown:.2f}%")
            logger.info(f"⚡ Sharpe ratio: {results.sharpe_ratio:.2f}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Error in backtest: {e}")
            raise
    
    def _calculate_performance_metrics(self, start_date: datetime, end_date: datetime,
                                     portfolio_values: List[float]) -> BacktestResults:
        """Calculate comprehensive performance metrics"""
        
        # Basic metrics
        initial_capital = portfolio_values[0]
        final_capital = portfolio_values[-1]
        total_return = final_capital - initial_capital
        total_return_pct = (total_return / initial_capital) * 100
        
        # Trade statistics
        completed_trades = [t for t in self.trades if t.pnl is not None]
        total_trades = len(completed_trades)
        
        if total_trades > 0:
            winning_trades = len([t for t in completed_trades if t.pnl > 0])
            losing_trades = len([t for t in completed_trades if t.pnl <= 0])
            win_rate = (winning_trades / total_trades) * 100
            
            wins = [t.pnl for t in completed_trades if t.pnl > 0]
            losses = [t.pnl for t in completed_trades if t.pnl <= 0]
            
            avg_win = np.mean(wins) if wins else 0
            avg_loss = np.mean(losses) if losses else 0
        else:
            winning_trades = 0
            losing_trades = 0
            win_rate = 0
            avg_win = 0
            avg_loss = 0
        
        # Calculate daily returns
        daily_returns = []
        for i in range(1, len(portfolio_values)):
            daily_return = (portfolio_values[i] - portfolio_values[i-1]) / portfolio_values[i-1]
            daily_returns.append(daily_return)
        
        # Sharpe ratio (assuming 0% risk-free rate)
        if len(daily_returns) > 1:
            sharpe_ratio = np.mean(daily_returns) / np.std(daily_returns) * np.sqrt(252)
        else:
            sharpe_ratio = 0
        
        # Maximum drawdown
        peak = portfolio_values[0]
        max_drawdown = 0
        
        for value in portfolio_values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        max_drawdown_pct = max_drawdown * 100
        
        return BacktestResults(
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital,
            final_capital=final_capital,
            total_return=total_return,
            total_return_pct=total_return_pct,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown_pct,
            win_rate=win_rate,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            avg_win=avg_win,
            avg_loss=avg_loss,
            trades=self.trades
        )

# Global backtesting engine
backtest_engine = RealBacktestingEngine()

async def initialize_backtesting():
    """Initialize the backtesting engine"""
    logger.info("Real backtesting engine initialized")
