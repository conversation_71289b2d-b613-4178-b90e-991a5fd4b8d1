#!/usr/bin/env python3
"""
QuantumEdge Financial Intelligence Tool - Live Paper Trading Demo
Demonstrates controlled paper trading deployment with Alpaca integration
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime

# Add src to path
sys.path.append('src')

from src.live_paper_trading import live_trading_system
from src.alpaca_trading_client import alpaca_client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('quantumedge.live_demo')

async def test_alpaca_connection():
    """Test Alpaca API connection and credentials"""
    logger.info("🔍 Testing Alpaca API Connection")
    logger.info("-" * 50)
    
    try:
        # Check environment variables
        api_key = os.getenv('ALPACA_API_KEY')
        api_secret = os.getenv('ALPACA_API_SECRET')
        
        if not api_key or not api_secret:
            logger.error("❌ Alpaca API credentials not found in environment")
            logger.error("Please ensure ALPACA_API_KEY and ALPACA_API_SECRET are set in .env file")
            return False
        
        logger.info(f"✅ API Key found: {api_key[:8]}...")
        logger.info(f"✅ API Secret found: {api_secret[:8]}...")
        
        # Initialize client
        await alpaca_client.initialize()
        
        # Get account info
        account_info = await alpaca_client.get_account_info()
        
        if 'error' in account_info:
            logger.error(f"❌ Failed to connect to Alpaca: {account_info['error']}")
            return False
        
        logger.info("✅ Successfully connected to Alpaca Paper Trading")
        logger.info(f"📊 Account Number: {account_info['account_number']}")
        logger.info(f"💰 Account Equity: ${account_info['equity']:,.2f}")
        logger.info(f"💵 Cash Available: ${account_info['cash']:,.2f}")
        logger.info(f"🔄 Buying Power: ${account_info['buying_power']:,.2f}")
        logger.info(f"📈 Portfolio Value: ${account_info['portfolio_value']:,.2f}")
        
        # Check if we have any existing positions
        if account_info['positions']:
            logger.info(f"📊 Current Positions: {account_info['positions_count']}")
            for pos in account_info['positions']:
                logger.info(f"   {pos['symbol']}: {pos['qty']} shares, ${pos['market_value']:,.2f} value")
        else:
            logger.info("📊 No current positions")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing Alpaca connection: {e}")
        return False

async def run_pre_market_analysis_demo():
    """Demonstrate pre-market analysis functionality"""
    logger.info("\n📊 PRE-MARKET ANALYSIS DEMO")
    logger.info("-" * 50)
    
    try:
        # Run pre-market analysis
        analysis_results = await live_trading_system.run_pre_market_analysis()
        
        if 'error' in analysis_results:
            logger.error(f"❌ Analysis failed: {analysis_results['error']}")
            return False
        
        # Display results
        logger.info(f"✅ Analysis completed for {len(analysis_results['symbols_analyzed'])} symbols")
        
        # Market regime
        regime_data = analysis_results['market_context']['regime']
        current_regime = regime_data['regime_labels'][regime_data['current_regime']]
        logger.info(f"🌍 Market Regime: {current_regime} (confidence: {regime_data['confidence']:.2f})")
        
        # Symbol analysis summary
        logger.info("\n📈 Symbol Analysis Summary:")
        for symbol, analysis in analysis_results['symbol_analysis'].items():
            risk_metrics = analysis['risk_metrics']
            sentiment = analysis['sentiment']
            
            logger.info(f"   {symbol}:")
            logger.info(f"     Data Quality: {analysis['market_data_quality']:.3f}")
            logger.info(f"     VaR (95%): {risk_metrics['var_95']:.3f}")
            logger.info(f"     Volatility: {risk_metrics['volatility']:.3f}")
            logger.info(f"     Sentiment: {sentiment.get('overall_sentiment', {}).get('score', 0):.3f}")
        
        # Trading signals
        signals = analysis_results['trading_signals']
        logger.info(f"\n🎯 Trading Signals Generated: {len(signals)}")
        
        for signal in signals:
            logger.info(f"   {signal['symbol']}: {signal['action']}")
            logger.info(f"     Confidence: {signal['confidence']:.3f}")
            logger.info(f"     Sentiment: {signal['sentiment_score']:.3f}")
            logger.info(f"     Risk: {signal['risk_score']:.3f}")
            logger.info(f"     Position Size: ${signal['position_size']:.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in pre-market analysis demo: {e}")
        return False

async def run_paper_trading_demo():
    """Demonstrate paper trading execution"""
    logger.info("\n🚀 PAPER TRADING EXECUTION DEMO")
    logger.info("-" * 50)
    
    try:
        # Execute any pending trading signals
        executions = await live_trading_system.execute_trading_signals()
        
        if not executions:
            logger.info("📊 No trading signals to execute")
            return True
        
        logger.info(f"🚀 Executed {len(executions)} trading signals")
        
        # Display execution results
        for execution in executions:
            signal = execution.signal
            logger.info(f"\n📈 Trade Execution: {signal.symbol}")
            logger.info(f"   Action: {signal.action}")
            logger.info(f"   Status: {execution.status}")
            logger.info(f"   Order ID: {execution.order_id}")
            logger.info(f"   Execution Price: ${execution.execution_price:.2f}" if execution.execution_price else "   Execution Price: Pending")
            
            if execution.error_message:
                logger.warning(f"   Error: {execution.error_message}")
        
        # Check order status for submitted orders
        logger.info("\n📊 Checking Order Status:")
        for execution in executions:
            if execution.order_id:
                order_status = await alpaca_client.get_order_status(execution.order_id)
                
                if 'error' not in order_status:
                    logger.info(f"   {execution.signal.symbol} ({execution.order_id}): {order_status['status']}")
                    if order_status.get('filled_avg_price'):
                        logger.info(f"     Filled at: ${order_status['filled_avg_price']:.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in paper trading demo: {e}")
        return False

async def run_performance_monitoring_demo():
    """Demonstrate performance monitoring"""
    logger.info("\n📈 PERFORMANCE MONITORING DEMO")
    logger.info("-" * 50)
    
    try:
        # Get current performance
        monitoring_data = await live_trading_system.monitor_positions()
        
        if 'error' in monitoring_data:
            logger.error(f"❌ Monitoring failed: {monitoring_data['error']}")
            return False
        
        # Display portfolio performance
        portfolio = monitoring_data['portfolio_performance']
        logger.info("💰 Portfolio Performance:")
        logger.info(f"   Total Equity: ${portfolio['total_equity']:,.2f}")
        logger.info(f"   Total P&L: ${portfolio['total_pl']:,.2f} ({portfolio['total_pl_pct']:+.2f}%)")
        logger.info(f"   Total Positions: {portfolio['total_positions']}")
        logger.info(f"   Win Rate: {portfolio['win_rate']:.1f}%")
        
        # Display trading stats
        stats = monitoring_data['trading_stats']
        logger.info("\n📊 Trading Statistics:")
        logger.info(f"   Total Trades: {stats['total_trades']}")
        logger.info(f"   Winning Trades: {stats['winning_trades']}")
        logger.info(f"   Win Rate: {stats['win_rate']:.1f}%")
        
        # Display system status
        system = monitoring_data['system_status']
        logger.info("\n🖥️ System Status:")
        logger.info(f"   Trading Active: {system['trading_active']}")
        logger.info(f"   Watchlist Size: {system['watchlist_size']}")
        logger.info(f"   Signals Pending: {system['signals_pending']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in performance monitoring demo: {e}")
        return False

async def run_complete_trading_cycle_demo():
    """Demonstrate complete daily trading cycle"""
    logger.info("\n🌅 COMPLETE TRADING CYCLE DEMO")
    logger.info("-" * 50)
    
    try:
        # Run complete daily cycle
        cycle_results = await live_trading_system.run_daily_trading_cycle()
        
        if 'error' in cycle_results:
            logger.error(f"❌ Trading cycle failed: {cycle_results['error']}")
            return False
        
        # Display cycle summary
        summary = cycle_results['cycle_summary']
        logger.info("✅ Daily Trading Cycle Completed")
        logger.info(f"   Duration: {summary['cycle_duration_seconds']:.2f} seconds")
        logger.info(f"   Symbols Analyzed: {summary['symbols_analyzed']}")
        logger.info(f"   Signals Generated: {summary['signals_generated']}")
        logger.info(f"   Trades Executed: {summary['trades_executed']}")
        logger.info(f"   Successful Trades: {summary['successful_trades']}")
        
        # Save results to file
        results_file = f"trading_cycle_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(cycle_results, f, indent=2, default=str)
        
        logger.info(f"📄 Cycle results saved to: {results_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in complete trading cycle demo: {e}")
        return False

async def generate_performance_report_demo():
    """Generate and display performance report"""
    logger.info("\n📋 PERFORMANCE REPORT DEMO")
    logger.info("-" * 50)
    
    try:
        # Generate 7-day performance report
        report = await live_trading_system.generate_performance_report(days_back=7)
        
        if 'error' in report:
            logger.error(f"❌ Report generation failed: {report['error']}")
            return False
        
        # Display report summary
        logger.info("📊 7-Day Performance Report:")
        
        portfolio = report['portfolio_performance']
        logger.info(f"   Portfolio Equity: ${portfolio['total_equity']:,.2f}")
        logger.info(f"   Total P&L: ${portfolio['total_pl']:,.2f}")
        
        trading = report['trading_statistics']
        logger.info(f"   Total Trades: {trading['total_trades']}")
        logger.info(f"   Filled Trades: {trading['filled_trades']}")
        logger.info(f"   Win Rate: {trading['win_rate']:.1f}%")
        
        system = report['system_performance']
        logger.info(f"   Cycles Completed: {system['cycles_completed']}")
        logger.info(f"   Analysis Success Rate: {system['analysis_success_rate']:.1f}%")
        
        # Save report to file
        report_file = f"performance_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📄 Performance report saved to: {report_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error generating performance report: {e}")
        return False

async def main():
    """Main demo function"""
    logger.info("🚀 QUANTUMEDGE LIVE PAPER TRADING DEMO")
    logger.info("=" * 80)
    
    try:
        # 1. Test Alpaca connection
        if not await test_alpaca_connection():
            logger.error("❌ Alpaca connection test failed - cannot proceed")
            return
        
        # 2. Initialize live trading system
        logger.info("\n🔧 Initializing Live Trading System...")
        await live_trading_system.initialize()
        
        # 3. Run pre-market analysis demo
        if not await run_pre_market_analysis_demo():
            logger.error("❌ Pre-market analysis demo failed")
            return
        
        # 4. Run paper trading demo
        if not await run_paper_trading_demo():
            logger.error("❌ Paper trading demo failed")
            return
        
        # 5. Run performance monitoring demo
        if not await run_performance_monitoring_demo():
            logger.error("❌ Performance monitoring demo failed")
            return
        
        # 6. Run complete trading cycle demo
        if not await run_complete_trading_cycle_demo():
            logger.error("❌ Complete trading cycle demo failed")
            return
        
        # 7. Generate performance report
        if not await generate_performance_report_demo():
            logger.error("❌ Performance report demo failed")
            return
        
        # Final summary
        logger.info("\n🎉 LIVE PAPER TRADING DEMO COMPLETED SUCCESSFULLY")
        logger.info("=" * 80)
        logger.info("✅ All systems operational and ready for live paper trading")
        logger.info("📊 QuantumEdge analysis integrated with Alpaca execution")
        logger.info("🔒 Risk management and position sizing active")
        logger.info("📈 Performance monitoring and reporting functional")
        
        logger.info("\n🎯 NEXT STEPS FOR LIVE DEPLOYMENT:")
        logger.info("1. Schedule daily pre-market analysis (6:00 AM EST)")
        logger.info("2. Monitor system performance during market hours")
        logger.info("3. Review daily performance reports")
        logger.info("4. Adjust parameters based on results")
        logger.info("5. Scale position sizes after 2 weeks of profitable trading")
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ Demo interrupted by user")
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        raise

if __name__ == "__main__":
    # Run the live paper trading demo
    asyncio.run(main())
