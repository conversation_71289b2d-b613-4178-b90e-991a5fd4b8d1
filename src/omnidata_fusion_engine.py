"""
Dynamic Risk Co-Pilot - Omnidata Fusion Engine
Combines financial, sentiment, and satellite data for comprehensive risk assessment
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import sqlite3
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import yfinance as yf
import pickle
import os

from src.reddit_sentiment_engine import reddit_sentiment_engine
from src.satellite_data_engine import satellite_engine

logger = logging.getLogger('risk_copilot.omnidata_fusion')

@dataclass
class RiskAssessment:
    """Comprehensive risk assessment result"""
    symbol: str
    date: datetime
    risk_score: float  # 1-10 scale
    risk_level: str  # 'low', 'medium', 'high', 'critical'
    confidence: float
    contributing_factors: Dict[str, float]
    alerts: List[str]
    recommendations: List[str]
    
    def to_dict(self) -> Dict:
        return {
            'symbol': self.symbol,
            'date': self.date.isoformat(),
            'risk_score': self.risk_score,
            'risk_level': self.risk_level,
            'confidence': self.confidence,
            'contributing_factors': self.contributing_factors,
            'alerts': self.alerts,
            'recommendations': self.recommendations
        }

class OmnidataFusionEngine:
    """
    Core engine that fuses financial, sentiment, and satellite data
    for comprehensive portfolio risk assessment
    """
    
    def __init__(self, db_path: str = "risk_copilot.db"):
        self.db_path = db_path
        self.model = None
        self.scaler = StandardScaler()
        self.model_trained = False
        self.feature_columns = []
        
        # Risk thresholds
        self.risk_thresholds = {
            'low': 3.0,
            'medium': 5.0,
            'high': 7.0,
            'critical': 8.5
        }
        
        # Initialize database
        self._init_database()
    
    def _init_database(self):
        """Initialize database for fused data storage"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS fused_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    price REAL,
                    volume INTEGER,
                    volatility REAL,
                    beta REAL,
                    sentiment_score REAL,
                    sentiment_volume REAL,
                    ship_count INTEGER,
                    supply_chain_risk REAL,
                    market_regime TEXT,
                    risk_score REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date, symbol)
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS risk_assessments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    date TEXT NOT NULL,
                    risk_score REAL,
                    risk_level TEXT,
                    confidence REAL,
                    contributing_factors TEXT,
                    alerts TEXT,
                    recommendations TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Omnidata fusion database initialized")
            
        except Exception as e:
            logger.error(f"❌ Error initializing fusion database: {e}")
            raise
    
    async def collect_financial_data(self, symbol: str, days_back: int = 30) -> pd.DataFrame:
        """Collect and process financial data for a symbol"""
        try:
            # Get stock data
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=f"{days_back}d")
            
            if hist.empty:
                logger.warning(f"No financial data available for {symbol}")
                return pd.DataFrame()
            
            # Calculate technical indicators
            hist['returns'] = hist['Close'].pct_change()
            hist['volatility'] = hist['returns'].rolling(window=10).std() * np.sqrt(252)
            hist['sma_20'] = hist['Close'].rolling(window=20).mean()
            hist['price_vs_sma'] = (hist['Close'] - hist['sma_20']) / hist['sma_20']
            
            # Calculate beta vs SPY
            spy = yf.Ticker('SPY')
            spy_hist = spy.history(period=f"{days_back}d")
            
            if not spy_hist.empty and len(spy_hist) == len(hist):
                spy_returns = spy_hist['Close'].pct_change().dropna()
                stock_returns = hist['returns'].dropna()
                
                if len(spy_returns) > 10 and len(stock_returns) > 10:
                    # Align the data
                    min_len = min(len(spy_returns), len(stock_returns))
                    spy_returns = spy_returns.tail(min_len)
                    stock_returns = stock_returns.tail(min_len)
                    
                    covariance = np.cov(stock_returns, spy_returns)[0][1]
                    spy_variance = np.var(spy_returns)
                    beta = covariance / spy_variance if spy_variance > 0 else 1.0
                else:
                    beta = 1.0
            else:
                beta = 1.0
            
            hist['beta'] = beta
            
            # Clean data
            financial_data = hist[['Close', 'Volume', 'volatility', 'beta', 'price_vs_sma']].copy()
            financial_data.columns = ['price', 'volume', 'volatility', 'beta', 'price_vs_sma']
            financial_data = financial_data.dropna()
            
            logger.debug(f"Collected {len(financial_data)} days of financial data for {symbol}")
            return financial_data
            
        except Exception as e:
            logger.error(f"Error collecting financial data for {symbol}: {e}")
            return pd.DataFrame()
    
    async def fuse_daily_data(self, symbol: str, target_date: datetime = None) -> Dict[str, Any]:
        """
        Fuse all data sources for a specific symbol and date
        Returns comprehensive data dictionary
        """
        if target_date is None:
            target_date = datetime.utcnow()
        
        try:
            logger.info(f"🔄 Fusing omnidata for {symbol} on {target_date.date()}")
            
            # 1. Collect financial data
            financial_data = await self.collect_financial_data(symbol, days_back=30)
            
            if financial_data.empty:
                logger.warning(f"No financial data for {symbol}")
                return {}
            
            # Get latest financial metrics
            latest_financial = financial_data.iloc[-1]
            
            # 2. Get sentiment data
            sentiment_data = reddit_sentiment_engine.process_daily_sentiment(target_date)
            symbol_sentiment = sentiment_data.get(symbol)
            
            if symbol_sentiment:
                sentiment_score = symbol_sentiment.volume_weighted_sentiment
                sentiment_volume = symbol_sentiment.post_count
            else:
                sentiment_score = 0.0
                sentiment_volume = 0
            
            # 3. Get satellite/supply chain data
            ship_data = satellite_engine.process_port_activity(target_date)
            supply_chain_indicator = satellite_engine.get_supply_chain_indicator(days_back=7)
            
            # 4. Determine market regime
            market_regime = self._determine_market_regime(financial_data)
            
            # 5. Create fused data record
            fused_record = {
                'date': target_date.date().isoformat(),
                'symbol': symbol,
                'price': float(latest_financial['price']),
                'volume': int(latest_financial['volume']),
                'volatility': float(latest_financial['volatility']),
                'beta': float(latest_financial['beta']),
                'sentiment_score': sentiment_score,
                'sentiment_volume': sentiment_volume,
                'ship_count': ship_data.ship_count,
                'supply_chain_risk': supply_chain_indicator['risk_level'],
                'market_regime': market_regime,
                'price_vs_sma': float(latest_financial['price_vs_sma'])
            }
            
            # Store in database
            self._store_fused_data(fused_record)
            
            logger.info(f"✅ Successfully fused omnidata for {symbol}")
            return fused_record
            
        except Exception as e:
            logger.error(f"❌ Error fusing data for {symbol}: {e}")
            return {}
    
    def _determine_market_regime(self, financial_data: pd.DataFrame) -> str:
        """Determine current market regime based on price action"""
        try:
            if len(financial_data) < 20:
                return 'insufficient_data'
            
            # Calculate short and long term trends
            short_returns = financial_data['price'].pct_change().tail(5).mean()
            long_returns = financial_data['price'].pct_change().tail(20).mean()
            
            # Calculate volatility regime
            recent_vol = financial_data['volatility'].tail(5).mean()
            historical_vol = financial_data['volatility'].mean()
            
            # Classify regime
            if short_returns > 0.002 and long_returns > 0.001:
                if recent_vol < historical_vol * 1.2:
                    return 'bull_stable'
                else:
                    return 'bull_volatile'
            elif short_returns < -0.002 and long_returns < -0.001:
                if recent_vol > historical_vol * 1.5:
                    return 'bear_volatile'
                else:
                    return 'bear_stable'
            else:
                if recent_vol > historical_vol * 1.3:
                    return 'sideways_volatile'
                else:
                    return 'sideways_stable'
                    
        except Exception as e:
            logger.error(f"Error determining market regime: {e}")
            return 'unknown'
    
    def _store_fused_data(self, fused_record: Dict[str, Any]):
        """Store fused data record in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO fused_data 
                (date, symbol, price, volume, volatility, beta, sentiment_score, 
                 sentiment_volume, ship_count, supply_chain_risk, market_regime)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                fused_record['date'],
                fused_record['symbol'],
                fused_record['price'],
                fused_record['volume'],
                fused_record['volatility'],
                fused_record['beta'],
                fused_record['sentiment_score'],
                fused_record['sentiment_volume'],
                fused_record['ship_count'],
                fused_record['supply_chain_risk'],
                fused_record['market_regime']
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error storing fused data: {e}")
    
    def train_risk_model(self, retrain: bool = False) -> bool:
        """
        Train RandomForest model for risk prediction
        Uses historical data to predict next-day volatility
        """
        try:
            model_path = "risk_model.pkl"
            scaler_path = "risk_scaler.pkl"
            
            # Load existing model if available and not retraining
            if not retrain and os.path.exists(model_path) and os.path.exists(scaler_path):
                with open(model_path, 'rb') as f:
                    self.model = pickle.load(f)
                with open(scaler_path, 'rb') as f:
                    self.scaler = pickle.load(f)
                self.model_trained = True
                logger.info("✅ Loaded existing risk model")
                return True
            
            logger.info("🔄 Training new risk prediction model...")
            
            # Get training data from database
            conn = sqlite3.connect(self.db_path)
            df = pd.read_sql_query('SELECT * FROM fused_data ORDER BY date', conn)
            conn.close()
            
            if len(df) < 50:  # Need minimum data for training
                logger.warning("⚠️ Insufficient data for model training, using default model")
                self._create_default_model()
                return True
            
            # Prepare features
            feature_columns = [
                'volatility', 'beta', 'sentiment_score', 'sentiment_volume',
                'ship_count', 'supply_chain_risk'
            ]
            
            # Create target variable (high volatility next day)
            df_grouped = df.groupby('symbol').apply(self._prepare_target_variable).reset_index(drop=True)
            
            if len(df_grouped) < 30:
                logger.warning("⚠️ Insufficient processed data, using default model")
                self._create_default_model()
                return True
            
            # Prepare training data
            X = df_grouped[feature_columns].fillna(0)
            y = df_grouped['high_volatility_next_day']
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train model
            self.model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1  # Use all CPU cores (M1 Max optimization)
            )
            
            self.model.fit(X_train_scaled, y_train)
            
            # Evaluate model
            train_score = self.model.score(X_train_scaled, y_train)
            test_score = self.model.score(X_test_scaled, y_test)
            
            logger.info(f"✅ Model trained - Train accuracy: {train_score:.3f}, Test accuracy: {test_score:.3f}")
            
            # Save model
            with open(model_path, 'wb') as f:
                pickle.dump(self.model, f)
            with open(scaler_path, 'wb') as f:
                pickle.dump(self.scaler, f)
            
            self.feature_columns = feature_columns
            self.model_trained = True
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error training risk model: {e}")
            self._create_default_model()
            return False
    
    def _prepare_target_variable(self, group_df: pd.DataFrame) -> pd.DataFrame:
        """Prepare target variable for model training"""
        try:
            group_df = group_df.sort_values('date').copy()
            
            # Calculate next day volatility
            group_df['next_day_volatility'] = group_df['volatility'].shift(-1)
            
            # Define high volatility threshold (top 30%)
            volatility_threshold = group_df['volatility'].quantile(0.7)
            group_df['high_volatility_next_day'] = (group_df['next_day_volatility'] > volatility_threshold).astype(int)
            
            # Remove last row (no next day data)
            return group_df[:-1]
            
        except Exception as e:
            logger.error(f"Error preparing target variable: {e}")
            return group_df
    
    def _create_default_model(self):
        """Create a simple default model when insufficient data"""
        logger.info("📊 Creating default risk model")

        # Simple rule-based model
        class DefaultRiskModel:
            def predict_proba(self, X):
                # Simple heuristic based on volatility and sentiment
                risk_probs = []
                for row in X:
                    volatility = row[0] if len(row) > 0 else 0.2
                    sentiment = row[2] if len(row) > 2 else 0.0

                    # Higher volatility = higher risk
                    # Negative sentiment = higher risk
                    risk_prob = min(0.9, max(0.1, volatility * 2 + max(0, -sentiment) * 0.3))
                    risk_probs.append([1 - risk_prob, risk_prob])

                return np.array(risk_probs)

        self.model = DefaultRiskModel()
        self.model_trained = True
        self.feature_columns = ['volatility', 'beta', 'sentiment_score', 'sentiment_volume', 'ship_count', 'supply_chain_risk']

        # Fit scaler with dummy data for default model
        dummy_data = np.array([[0.2, 1.0, 0.0, 5, 15, 0.5]])
        self.scaler.fit(dummy_data)
    
    async def assess_portfolio_risk(self, portfolio_symbols: List[str]) -> Dict[str, RiskAssessment]:
        """
        Assess risk for entire portfolio
        Returns risk assessment for each symbol
        """
        try:
            logger.info(f"🎯 Assessing portfolio risk for {len(portfolio_symbols)} symbols")
            
            if not self.model_trained:
                self.train_risk_model()
            
            portfolio_assessments = {}
            
            for symbol in portfolio_symbols:
                try:
                    # Fuse current data
                    fused_data = await self.fuse_daily_data(symbol)
                    
                    if not fused_data:
                        logger.warning(f"No data available for {symbol}")
                        continue
                    
                    # Prepare features for prediction
                    features = [
                        fused_data.get('volatility', 0.2),
                        fused_data.get('beta', 1.0),
                        fused_data.get('sentiment_score', 0.0),
                        fused_data.get('sentiment_volume', 0),
                        fused_data.get('ship_count', 15),
                        fused_data.get('supply_chain_risk', 0.5)
                    ]
                    
                    # Scale features
                    features_scaled = self.scaler.transform([features])
                    
                    # Predict risk
                    risk_proba = self.model.predict_proba(features_scaled)[0][1]  # Probability of high risk
                    
                    # Convert to 1-10 scale
                    risk_score = 1 + (risk_proba * 9)
                    
                    # Determine risk level
                    if risk_score <= self.risk_thresholds['low']:
                        risk_level = 'low'
                    elif risk_score <= self.risk_thresholds['medium']:
                        risk_level = 'medium'
                    elif risk_score <= self.risk_thresholds['high']:
                        risk_level = 'high'
                    else:
                        risk_level = 'critical'
                    
                    # Generate alerts and recommendations
                    alerts, recommendations = self._generate_alerts_and_recommendations(symbol, fused_data, risk_score)
                    
                    # Create assessment
                    assessment = RiskAssessment(
                        symbol=symbol,
                        date=datetime.utcnow(),
                        risk_score=risk_score,
                        risk_level=risk_level,
                        confidence=0.8,  # Model confidence
                        contributing_factors={
                            'volatility': fused_data.get('volatility', 0) * 10,
                            'sentiment': abs(fused_data.get('sentiment_score', 0)) * 10,
                            'supply_chain': fused_data.get('supply_chain_risk', 0) * 10,
                            'market_regime': 5.0 if 'volatile' in fused_data.get('market_regime', '') else 3.0
                        },
                        alerts=alerts,
                        recommendations=recommendations
                    )
                    
                    portfolio_assessments[symbol] = assessment
                    
                    logger.info(f"✅ {symbol}: Risk Score {risk_score:.1f} ({risk_level})")
                    
                except Exception as e:
                    logger.error(f"Error assessing {symbol}: {e}")
                    continue
            
            return portfolio_assessments
            
        except Exception as e:
            logger.error(f"❌ Error assessing portfolio risk: {e}")
            return {}
    
    def _generate_alerts_and_recommendations(self, symbol: str, fused_data: Dict, risk_score: float) -> Tuple[List[str], List[str]]:
        """Generate specific alerts and recommendations based on data"""
        alerts = []
        recommendations = []
        
        try:
            # Volatility alerts
            if fused_data.get('volatility', 0) > 0.3:
                alerts.append(f"High volatility detected for {symbol} ({fused_data['volatility']:.1%})")
                recommendations.append("Consider reducing position size due to high volatility")
            
            # Sentiment alerts
            sentiment = fused_data.get('sentiment_score', 0)
            if sentiment < -0.3:
                alerts.append(f"Negative sentiment detected for {symbol} (score: {sentiment:.2f})")
                recommendations.append("Monitor news and social media for developing negative sentiment")
            elif sentiment > 0.5:
                recommendations.append("Positive sentiment may support price, but watch for overextension")
            
            # Supply chain alerts
            supply_risk = fused_data.get('supply_chain_risk', 0.5)
            if supply_risk > 0.7:
                alerts.append("Supply chain disruption risk elevated")
                recommendations.append("Monitor logistics and supply chain developments")
            
            # Market regime alerts
            regime = fused_data.get('market_regime', '')
            if 'bear' in regime:
                alerts.append("Bear market conditions detected")
                recommendations.append("Consider defensive positioning and risk management")
            elif 'volatile' in regime:
                alerts.append("High market volatility regime")
                recommendations.append("Increase monitoring frequency and consider tighter stops")
            
            # Overall risk alerts
            if risk_score > 8:
                alerts.append(f"CRITICAL RISK: {symbol} risk score {risk_score:.1f}/10")
                recommendations.append("Consider immediate risk reduction or position exit")
            elif risk_score > 6:
                alerts.append(f"HIGH RISK: {symbol} risk score {risk_score:.1f}/10")
                recommendations.append("Implement additional risk controls and monitoring")
            
        except Exception as e:
            logger.error(f"Error generating alerts: {e}")
        
        return alerts, recommendations

# Global fusion engine
omnidata_engine = OmnidataFusionEngine()

async def initialize_omnidata_engine():
    """Initialize omnidata fusion engine"""
    await omnidata_engine.train_risk_model()
    logger.info("Omnidata fusion engine initialized")
