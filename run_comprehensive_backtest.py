#!/usr/bin/env python3
"""
QuantumEdge Financial Intelligence Tool - Comprehensive 6-Month Historical Backtest
Validates strategy performance using real market data from January 2024 to June 2024
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timedelta
import pandas as pd

# Add src to path
sys.path.append('src')

from src.real_backtesting_engine import backtest_engine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('quantumedge.comprehensive_backtest')

async def run_comprehensive_6month_backtest():
    """
    Run comprehensive 6-month backtest on QuantumEdge strategy
    Period: January 1, 2024 to June 30, 2024
    Symbols: AAPL, MSFT, GOOGL, AMZN, TSLA
    """
    logger.info("🚀 QUANTUMEDGE COMPREHENSIVE 6-MONTH BACKTEST")
    logger.info("=" * 80)
    
    # Define backtest parameters
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 6, 30)
    
    logger.info(f"📊 Backtest Configuration:")
    logger.info(f"   Period: {start_date.date()} to {end_date.date()}")
    logger.info(f"   Duration: {(end_date - start_date).days} days")
    logger.info(f"   Symbols: {symbols}")
    logger.info(f"   Initial Capital: ${backtest_engine.initial_capital:,.2f}")
    logger.info(f"   Max Position Size: ${backtest_engine.max_position_size:.2f}")
    logger.info(f"   Stop Loss: {backtest_engine.stop_loss_pct*100:.1f}%")
    logger.info(f"   Take Profit: {backtest_engine.take_profit_pct*100:.1f}%")
    
    # Strategy parameters
    logger.info(f"\n🎯 Strategy Parameters:")
    logger.info(f"   Min Confidence: {backtest_engine.min_confidence:.1f}")
    logger.info(f"   Min Sentiment: {backtest_engine.min_sentiment:.1f}")
    logger.info(f"   Max Risk (CVaR): {backtest_engine.max_risk:.3f}")
    logger.info(f"   Min Regime Confidence: {backtest_engine.min_regime_confidence:.1f}")
    
    try:
        # Override the data method to use yfinance with proper timezone handling
        import yfinance as yf
        import pytz
        
        async def get_yfinance_data_with_tz(symbol: str, start_date: datetime, end_date: datetime):
            try:
                logger.info(f"📈 Fetching data for {symbol}...")
                ticker = yf.Ticker(symbol)
                hist = ticker.history(
                    start=start_date.strftime('%Y-%m-%d'),
                    end=end_date.strftime('%Y-%m-%d'),
                    interval='1d'
                )
                
                if hist.empty:
                    logger.warning(f"⚠️ No data available for {symbol}")
                    return pd.DataFrame()
                
                # Convert to expected format with proper timezone handling
                data = []
                eastern = pytz.timezone('US/Eastern')
                
                for date, row in hist.iterrows():
                    # Ensure date is timezone-aware
                    if date.tzinfo is None:
                        date = eastern.localize(date.replace(hour=16))  # Market close time
                    
                    data.append({
                        'timestamp': date,
                        'open': float(row['Open']),
                        'high': float(row['High']),
                        'low': float(row['Low']),
                        'close': float(row['Close']),
                        'volume': int(row['Volume'])
                    })
                
                df = pd.DataFrame(data)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.sort_values('timestamp')
                
                logger.info(f"✅ Loaded {len(df)} days of data for {symbol}")
                return df
                
            except Exception as e:
                logger.error(f"❌ Error fetching data for {symbol}: {e}")
                return pd.DataFrame()
        
        # Replace the method temporarily
        original_method = backtest_engine.get_historical_data
        backtest_engine.get_historical_data = get_yfinance_data_with_tz
        
        # Run the comprehensive backtest
        logger.info(f"\n🔄 Starting comprehensive backtest execution...")
        start_time = datetime.utcnow()
        
        results = await backtest_engine.run_backtest(symbols, start_date, end_date)
        
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Restore original method
        backtest_engine.get_historical_data = original_method
        
        # Analyze and display results
        logger.info(f"\n📊 COMPREHENSIVE BACKTEST RESULTS")
        logger.info("=" * 80)
        logger.info(f"⏱️ Execution Time: {execution_time:.1f} seconds")
        
        # Performance Summary
        logger.info(f"\n💰 PERFORMANCE SUMMARY:")
        logger.info(f"   Initial Capital: ${results.initial_capital:,.2f}")
        logger.info(f"   Final Capital: ${results.final_capital:,.2f}")
        logger.info(f"   Total Return: ${results.total_return:,.2f}")
        logger.info(f"   Total Return %: {results.total_return_pct:+.2f}%")
        logger.info(f"   Annualized Return: {(results.total_return_pct * 2):.2f}%")  # 6 months * 2
        
        # Risk Metrics
        logger.info(f"\n📉 RISK METRICS:")
        logger.info(f"   Sharpe Ratio: {results.sharpe_ratio:.2f}")
        logger.info(f"   Max Drawdown: {results.max_drawdown:.2f}%")
        logger.info(f"   Volatility: {results.sharpe_ratio * np.sqrt(252) if results.sharpe_ratio != 0 else 0:.2f}%")
        
        # Trading Statistics
        logger.info(f"\n📈 TRADING STATISTICS:")
        logger.info(f"   Total Trades: {results.total_trades}")
        logger.info(f"   Winning Trades: {results.winning_trades}")
        logger.info(f"   Losing Trades: {results.losing_trades}")
        logger.info(f"   Win Rate: {results.win_rate:.1f}%")
        logger.info(f"   Average Win: ${results.avg_win:.2f}")
        logger.info(f"   Average Loss: ${results.avg_loss:.2f}")
        
        if results.avg_loss != 0:
            profit_factor = abs(results.avg_win / results.avg_loss)
            logger.info(f"   Profit Factor: {profit_factor:.2f}")
        
        # Monthly Performance Breakdown
        if results.trades:
            logger.info(f"\n📅 MONTHLY PERFORMANCE BREAKDOWN:")
            monthly_trades = {}
            monthly_pnl = {}
            
            for trade in results.trades:
                if trade.pnl is not None:
                    month_key = trade.entry_date.strftime('%Y-%m')
                    if month_key not in monthly_trades:
                        monthly_trades[month_key] = 0
                        monthly_pnl[month_key] = 0
                    monthly_trades[month_key] += 1
                    monthly_pnl[month_key] += trade.pnl
            
            for month in sorted(monthly_trades.keys()):
                logger.info(f"   {month}: {monthly_trades[month]} trades, "
                           f"${monthly_pnl[month]:+.2f} P&L")
        
        # Symbol Performance Analysis
        if results.trades:
            logger.info(f"\n📊 SYMBOL PERFORMANCE ANALYSIS:")
            symbol_stats = {}
            
            for trade in results.trades:
                if trade.pnl is not None:
                    symbol = trade.symbol
                    if symbol not in symbol_stats:
                        symbol_stats[symbol] = {'trades': 0, 'pnl': 0, 'wins': 0}
                    symbol_stats[symbol]['trades'] += 1
                    symbol_stats[symbol]['pnl'] += trade.pnl
                    if trade.pnl > 0:
                        symbol_stats[symbol]['wins'] += 1
            
            for symbol in sorted(symbol_stats.keys()):
                stats = symbol_stats[symbol]
                win_rate = (stats['wins'] / stats['trades'] * 100) if stats['trades'] > 0 else 0
                logger.info(f"   {symbol}: {stats['trades']} trades, "
                           f"${stats['pnl']:+.2f} P&L, {win_rate:.1f}% win rate")
        
        # Performance Assessment
        logger.info(f"\n🎯 QUANTUMEDGE STRATEGY ASSESSMENT:")
        
        performance_score = 0
        max_score = 6
        
        # Profitability (2 points)
        if results.total_return_pct > 10:  # >10% in 6 months
            logger.info("✅ Excellent profitability (>10% in 6 months) (+2 points)")
            performance_score += 2
        elif results.total_return_pct > 5:  # >5% in 6 months
            logger.info("✅ Good profitability (>5% in 6 months) (+1 point)")
            performance_score += 1
        elif results.total_return_pct > 0:
            logger.info("⚠️ Modest profitability (+0.5 points)")
            performance_score += 0.5
        else:
            logger.info("❌ Strategy was unprofitable (0 points)")
        
        # Win Rate (1 point)
        if results.win_rate >= 60:
            logger.info("✅ Win rate meets 60% target (+1 point)")
            performance_score += 1
        else:
            logger.info(f"⚠️ Win rate ({results.win_rate:.1f}%) below 60% target (0 points)")
        
        # Sharpe Ratio (1 point)
        if results.sharpe_ratio >= 1.0:
            logger.info("✅ Sharpe ratio meets 1.0+ target (+1 point)")
            performance_score += 1
        else:
            logger.info(f"⚠️ Sharpe ratio ({results.sharpe_ratio:.2f}) below 1.0 target (0 points)")
        
        # Max Drawdown (1 point)
        if results.max_drawdown <= 10:
            logger.info("✅ Max drawdown within 10% limit (+1 point)")
            performance_score += 1
        else:
            logger.info(f"⚠️ Max drawdown ({results.max_drawdown:.2f}%) exceeds 10% limit (0 points)")
        
        # Trading Activity (1 point)
        if results.total_trades >= 20:  # At least 20 trades in 6 months
            logger.info("✅ Sufficient trading activity (+1 point)")
            performance_score += 1
        else:
            logger.info(f"⚠️ Low trading activity ({results.total_trades} trades) (0 points)")
        
        logger.info(f"\n🏆 OVERALL STRATEGY SCORE: {performance_score:.1f}/{max_score}")
        
        # Final Assessment
        if performance_score >= 5:
            logger.info("🎉 EXCELLENT - Strategy ready for live paper trading!")
            recommendation = "DEPLOY"
        elif performance_score >= 4:
            logger.info("✅ GOOD - Strategy shows strong promise, minor optimizations recommended")
            recommendation = "OPTIMIZE_THEN_DEPLOY"
        elif performance_score >= 3:
            logger.info("⚠️ FAIR - Strategy needs improvement before live deployment")
            recommendation = "IMPROVE"
        else:
            logger.info("❌ POOR - Strategy requires significant refinement")
            recommendation = "REDESIGN"
        
        # Save comprehensive results
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        results_file = f"comprehensive_backtest_{timestamp}.json"
        
        comprehensive_results = {
            'backtest_config': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'symbols': symbols,
                'duration_days': (end_date - start_date).days,
                'execution_time_seconds': execution_time
            },
            'performance_summary': {
                'initial_capital': results.initial_capital,
                'final_capital': results.final_capital,
                'total_return': results.total_return,
                'total_return_pct': results.total_return_pct,
                'annualized_return_pct': results.total_return_pct * 2,
                'sharpe_ratio': results.sharpe_ratio,
                'max_drawdown': results.max_drawdown,
                'win_rate': results.win_rate
            },
            'trading_statistics': {
                'total_trades': results.total_trades,
                'winning_trades': results.winning_trades,
                'losing_trades': results.losing_trades,
                'avg_win': results.avg_win,
                'avg_loss': results.avg_loss
            },
            'assessment': {
                'performance_score': performance_score,
                'max_score': max_score,
                'recommendation': recommendation
            },
            'detailed_results': results.to_dict()
        }
        
        with open(results_file, 'w') as f:
            json.dump(comprehensive_results, f, indent=2, default=str)
        
        logger.info(f"\n📄 Comprehensive results saved to: {results_file}")
        
        # Next Steps
        logger.info(f"\n🎯 RECOMMENDED NEXT STEPS:")
        
        if recommendation == "DEPLOY":
            logger.info("1. ✅ Begin live paper trading with small position sizes")
            logger.info("2. 📊 Monitor real-time performance for 2 weeks")
            logger.info("3. 📈 Scale position sizes if performance maintains")
            logger.info("4. 🚀 Consider migration to live trading after validation")
        elif recommendation == "OPTIMIZE_THEN_DEPLOY":
            logger.info("1. 🔧 Fine-tune confidence thresholds and risk parameters")
            logger.info("2. 📊 Run additional backtests on different time periods")
            logger.info("3. ✅ Begin conservative paper trading")
            logger.info("4. 📈 Monitor and adjust based on live performance")
        elif recommendation == "IMPROVE":
            logger.info("1. 🔍 Analyze losing trades for pattern identification")
            logger.info("2. 🔧 Enhance signal generation algorithms")
            logger.info("3. 📊 Test different risk management parameters")
            logger.info("4. 🔄 Re-run backtest after improvements")
        else:
            logger.info("1. 🔬 Fundamental strategy review required")
            logger.info("2. 📊 Analyze market regime performance")
            logger.info("3. 🔧 Consider alternative signal generation methods")
            logger.info("4. 📈 Test on different asset classes or timeframes")
        
        return results, comprehensive_results
        
    except Exception as e:
        logger.error(f"❌ Comprehensive backtest failed: {e}")
        raise

async def main():
    """Main execution function"""
    try:
        results, comprehensive_results = await run_comprehensive_6month_backtest()
        
        logger.info(f"\n🎉 COMPREHENSIVE BACKTEST COMPLETED SUCCESSFULLY")
        logger.info("=" * 80)
        
        # Quick summary for immediate assessment
        logger.info(f"📊 QUICK SUMMARY:")
        logger.info(f"   6-Month Return: {results.total_return_pct:+.2f}%")
        logger.info(f"   Win Rate: {results.win_rate:.1f}%")
        logger.info(f"   Sharpe Ratio: {results.sharpe_ratio:.2f}")
        logger.info(f"   Max Drawdown: {results.max_drawdown:.2f}%")
        logger.info(f"   Total Trades: {results.total_trades}")
        logger.info(f"   Recommendation: {comprehensive_results['assessment']['recommendation']}")
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ Backtest interrupted by user")
    except Exception as e:
        logger.error(f"❌ Backtest execution failed: {e}")
        raise

if __name__ == "__main__":
    # Import numpy for calculations
    import numpy as np
    
    # Run the comprehensive 6-month backtest
    asyncio.run(main())
