"""
QuantumEdge Financial Intelligence Tool - Alternative Data Tests
Comprehensive testing of SEC filings, patent analysis, and economic indicators
"""

import pytest
import asyncio
import sys
import os
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.alternative_data import (
    SECDataClient, PatentDataClient, EconomicDataClient, AlternativeDataEngine,
    SECFiling, PatentData, EconomicIndicator
)

class TestSECFiling:
    """Test SEC filing data structure"""
    
    def test_sec_filing_creation(self):
        """Test SEC filing creation and serialization"""
        filing = SECFiling(
            company_name="Test Company",
            cik="0001234567",
            form_type="10-K",
            filing_date=datetime.utcnow(),
            report_date=datetime.utcnow() - timedelta(days=30),
            document_url="https://www.sec.gov/test",
            summary="Test filing summary",
            sentiment_score=0.75
        )
        
        data_dict = filing.to_dict()
        
        assert data_dict['company_name'] == "Test Company"
        assert data_dict['cik'] == "0001234567"
        assert data_dict['form_type'] == "10-K"
        assert data_dict['summary'] == "Test filing summary"
        assert data_dict['sentiment_score'] == 0.75
        assert 'filing_date' in data_dict
        assert 'report_date' in data_dict

class TestPatentData:
    """Test patent data structure"""
    
    def test_patent_data_creation(self):
        """Test patent data creation and serialization"""
        patent = PatentData(
            patent_number="US10000001",
            title="Test Patent",
            assignee="Test Company",
            filing_date=datetime.utcnow() - timedelta(days=365),
            grant_date=datetime.utcnow() - timedelta(days=180),
            abstract="Test patent abstract",
            classification="G06Q 40/00",
            innovation_score=0.85
        )
        
        data_dict = patent.to_dict()
        
        assert data_dict['patent_number'] == "US10000001"
        assert data_dict['title'] == "Test Patent"
        assert data_dict['assignee'] == "Test Company"
        assert data_dict['abstract'] == "Test patent abstract"
        assert data_dict['classification'] == "G06Q 40/00"
        assert data_dict['innovation_score'] == 0.85

class TestEconomicIndicator:
    """Test economic indicator data structure"""
    
    def test_economic_indicator_creation(self):
        """Test economic indicator creation and serialization"""
        indicator = EconomicIndicator(
            indicator_name="GDP_GROWTH",
            value=2.5,
            timestamp=datetime.utcnow(),
            source="BEA",
            unit="percent",
            frequency="quarterly"
        )
        
        data_dict = indicator.to_dict()
        
        assert data_dict['indicator_name'] == "GDP_GROWTH"
        assert data_dict['value'] == 2.5
        assert data_dict['source'] == "BEA"
        assert data_dict['unit'] == "percent"
        assert data_dict['frequency'] == "quarterly"

class TestSECDataClient:
    """Test SEC data client"""
    
    @pytest.mark.asyncio
    async def test_sec_client_initialization(self):
        """Test SEC client initialization"""
        async with SECDataClient() as client:
            assert client.session is not None
            assert client.base_url == "https://data.sec.gov"
            assert 'User-Agent' in client.headers
    
    @pytest.mark.asyncio
    async def test_cik_lookup_format(self):
        """Test CIK formatting"""
        async with SECDataClient() as client:
            # Test with mock data (since we can't rely on external API in tests)
            # This tests the internal logic
            assert client is not None
    
    @pytest.mark.asyncio
    async def test_recent_filings_structure(self):
        """Test recent filings data structure"""
        async with SECDataClient() as client:
            # Test with mock CIK
            filings = await client.get_recent_filings("0001234567", ["10-K", "10-Q"], limit=3)
            
            # Should return list (may be empty due to mock data)
            assert isinstance(filings, list)
            
            # If filings exist, check structure
            for filing in filings:
                assert isinstance(filing, SECFiling)
                assert filing.company_name is not None
                assert filing.cik is not None
                assert filing.form_type in ["10-K", "10-Q", "8-K"]

class TestPatentDataClient:
    """Test patent data client"""
    
    @pytest.mark.asyncio
    async def test_patent_client_initialization(self):
        """Test patent client initialization"""
        async with PatentDataClient() as client:
            assert client.session is not None
            assert client.base_url == "https://developer.uspto.gov"
    
    @pytest.mark.asyncio
    async def test_patent_search_by_assignee(self):
        """Test patent search functionality"""
        async with PatentDataClient() as client:
            patents = await client.search_patents_by_assignee("Apple Inc", limit=5)
            
            # Should return list of patents (mock data)
            assert isinstance(patents, list)
            assert len(patents) <= 5
            
            # Check patent structure
            for patent in patents:
                assert isinstance(patent, PatentData)
                assert patent.patent_number is not None
                assert patent.title is not None
                assert patent.assignee == "Apple Inc"
                assert patent.abstract is not None
    
    def test_mock_patent_generation(self):
        """Test mock patent data generation"""
        client = PatentDataClient()
        patents = client._generate_mock_patents("Test Company", 3)
        
        assert len(patents) == 3
        
        for patent in patents:
            assert isinstance(patent, PatentData)
            assert patent.assignee == "Test Company"
            assert patent.innovation_score is not None
            assert 0.0 <= patent.innovation_score <= 1.0

class TestEconomicDataClient:
    """Test economic data client"""
    
    def setup_method(self):
        self.client = EconomicDataClient()
    
    @pytest.mark.asyncio
    async def test_economic_indicators_retrieval(self):
        """Test economic indicators retrieval"""
        indicators = await self.client.get_economic_indicators()
        
        # Should return list of indicators
        assert isinstance(indicators, list)
        assert len(indicators) > 0
        
        # Check indicator structure
        for indicator in indicators:
            assert isinstance(indicator, EconomicIndicator)
            assert indicator.indicator_name in self.client.indicators
            assert isinstance(indicator.value, (int, float))
            assert indicator.source is not None
            assert indicator.unit is not None
            assert indicator.frequency is not None
    
    def test_indicator_configuration(self):
        """Test economic indicator configuration"""
        expected_indicators = ['GDP_GROWTH', 'UNEMPLOYMENT_RATE', 'INFLATION_RATE', 'INTEREST_RATE', 'VIX']
        
        for indicator_name in expected_indicators:
            assert indicator_name in self.client.indicators
            
            config = self.client.indicators[indicator_name]
            assert 'source' in config
            assert 'unit' in config
            assert 'frequency' in config

class TestAlternativeDataEngine:
    """Test alternative data engine"""
    
    def setup_method(self):
        self.engine = AlternativeDataEngine()
    
    @pytest.mark.asyncio
    async def test_engine_initialization(self):
        """Test alternative data engine initialization"""
        assert self.engine.economic_client is not None
        assert self.engine.validator is not None
    
    @pytest.mark.asyncio
    async def test_company_fundamentals_analysis(self):
        """Test company fundamentals analysis"""
        # Test with mock ticker
        result = await self.engine.analyze_company_fundamentals("AAPL")
        
        # Check result structure
        assert 'ticker' in result
        assert 'timestamp' in result
        assert 'sec_filings' in result
        assert 'patent_portfolio' in result
        assert 'sentiment_analysis' in result
        assert 'innovation_metrics' in result
        
        assert result['ticker'] == "AAPL"
        
        # Check that data structures are correct types
        assert isinstance(result['sec_filings'], list)
        assert isinstance(result['patent_portfolio'], list)
        assert isinstance(result['sentiment_analysis'], dict)
        assert isinstance(result['innovation_metrics'], dict)
    
    @pytest.mark.asyncio
    async def test_market_context_analysis(self):
        """Test market context analysis"""
        context = await self.engine.get_market_context()
        
        # Check context structure
        assert 'timestamp' in context
        assert 'economic_indicators' in context
        assert 'market_regime_indicators' in context
        assert 'risk_factors' in context
        
        # Check that economic indicators are present
        assert isinstance(context['economic_indicators'], list)
        assert len(context['economic_indicators']) > 0
        
        # Check risk factors
        assert isinstance(context['risk_factors'], list)
    
    @pytest.mark.asyncio
    async def test_alternative_data_report_generation(self):
        """Test comprehensive alternative data report"""
        symbols = ['AAPL', 'MSFT']
        report = await self.engine.generate_alternative_data_report(symbols)
        
        # Check report structure
        assert 'timestamp' in report
        assert 'symbols_analyzed' in report
        assert 'company_analyses' in report
        assert 'market_context' in report
        assert 'summary_insights' in report
        
        assert report['symbols_analyzed'] == symbols
        
        # Check company analyses
        assert isinstance(report['company_analyses'], dict)
        for symbol in symbols:
            assert symbol in report['company_analyses']
            
            analysis = report['company_analyses'][symbol]
            assert 'ticker' in analysis
            assert 'sec_filings' in analysis
            assert 'patent_portfolio' in analysis
        
        # Check market context
        assert isinstance(report['market_context'], dict)
        
        # Check summary insights
        assert isinstance(report['summary_insights'], list)

class TestIntegration:
    """Integration tests for alternative data system"""
    
    @pytest.mark.asyncio
    async def test_alternative_data_initialization(self):
        """Test alternative data system initialization"""
        from src.alternative_data import initialize_alternative_data
        
        # Should initialize without errors
        await initialize_alternative_data()
    
    @pytest.mark.asyncio
    async def test_end_to_end_analysis(self):
        """Test complete alternative data analysis workflow"""
        engine = AlternativeDataEngine()
        
        # Test single company analysis
        company_result = await engine.analyze_company_fundamentals("AAPL")
        
        # Should have all required components
        assert 'sec_filings' in company_result
        assert 'patent_portfolio' in company_result
        assert 'innovation_metrics' in company_result
        
        # Test market context
        market_context = await engine.get_market_context()
        
        # Should have economic indicators
        assert 'economic_indicators' in market_context
        assert len(market_context['economic_indicators']) > 0
        
        # Test comprehensive report
        report = await engine.generate_alternative_data_report(['AAPL'])
        
        # Should combine all analyses
        assert 'company_analyses' in report
        assert 'market_context' in report
        assert 'AAPL' in report['company_analyses']
    
    @pytest.mark.asyncio
    async def test_performance_requirements(self):
        """Test alternative data system performance"""
        engine = AlternativeDataEngine()
        
        start_time = datetime.utcnow()
        
        # Analyze multiple companies
        symbols = ['AAPL', 'MSFT', 'GOOGL']
        report = await engine.generate_alternative_data_report(symbols)
        
        end_time = datetime.utcnow()
        processing_time = (end_time - start_time).total_seconds()
        
        # Should complete within reasonable time
        assert processing_time < 30.0  # 30 seconds max for 3 companies
        
        # Should have analyzed all symbols
        assert len(report['company_analyses']) == len(symbols)
        
        # All analyses should be complete
        for symbol in symbols:
            analysis = report['company_analyses'][symbol]
            assert 'ticker' in analysis
            assert analysis['ticker'] == symbol
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in alternative data system"""
        engine = AlternativeDataEngine()
        
        # Test with invalid/empty ticker
        result = await engine.analyze_company_fundamentals("")
        
        # Should handle gracefully
        assert isinstance(result, dict)
        assert 'ticker' in result
        
        # Test with multiple invalid tickers
        report = await engine.generate_alternative_data_report(['INVALID1', 'INVALID2'])
        
        # Should handle gracefully
        assert isinstance(report, dict)
        assert 'company_analyses' in report

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
