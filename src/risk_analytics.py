"""
QuantumEdge Financial Intelligence Tool - Advanced Risk Analytics Engine
CVaR models, regime detection, and portfolio stress testing with PyTorch MPS acceleration
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from dataclasses import dataclass
from sklearn.mixture import GaussianMixture
from sklearn.preprocessing import StandardScaler
import duckdb
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

from src.config import config
from src.market_data_pipeline import MarketDataStorage

logger = logging.getLogger('quantumedge.risk_analytics')

@dataclass
class RiskMetrics:
    """Risk analysis results"""
    symbol: str
    timestamp: datetime
    var_95: float
    var_99: float
    cvar_95: float
    cvar_99: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    beta: Optional[float] = None
    
    def to_dict(self) -> Dict:
        return {
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'var_95': self.var_95,
            'var_99': self.var_99,
            'cvar_95': self.cvar_95,
            'cvar_99': self.cvar_99,
            'volatility': self.volatility,
            'sharpe_ratio': self.sharpe_ratio,
            'max_drawdown': self.max_drawdown,
            'beta': self.beta
        }

@dataclass
class RegimeDetectionResult:
    """Market regime detection result"""
    timestamp: datetime
    current_regime: int
    regime_probabilities: List[float]
    regime_labels: List[str]
    confidence: float
    
    def to_dict(self) -> Dict:
        return {
            'timestamp': self.timestamp.isoformat(),
            'current_regime': self.current_regime,
            'regime_probabilities': self.regime_probabilities,
            'regime_labels': self.regime_labels,
            'confidence': self.confidence
        }

class CVaRCalculator:
    """Conditional Value at Risk calculator with Monte Carlo simulation"""
    
    def __init__(self, confidence_levels: List[float] = [0.95, 0.99]):
        self.confidence_levels = confidence_levels
        self.device = torch.device("mps" if torch.backends.mps.is_available() else "cpu")
        
    def calculate_var_cvar(self, returns: np.ndarray, 
                          confidence_levels: Optional[List[float]] = None) -> Dict[str, float]:
        """
        Calculate VaR and CVaR using historical simulation
        
        Args:
            returns: Array of historical returns
            confidence_levels: List of confidence levels (default: [0.95, 0.99])
            
        Returns:
            Dictionary with VaR and CVaR values
        """
        if confidence_levels is None:
            confidence_levels = self.confidence_levels
        
        if len(returns) < 30:
            logger.warning("Insufficient data for reliable VaR/CVaR calculation")
            return {f'var_{int(cl*100)}': 0.0 for cl in confidence_levels} | \
                   {f'cvar_{int(cl*100)}': 0.0 for cl in confidence_levels}
        
        # Sort returns in ascending order
        sorted_returns = np.sort(returns)
        results = {}
        
        for cl in confidence_levels:
            # Calculate VaR
            var_index = int((1 - cl) * len(sorted_returns))
            var = sorted_returns[var_index] if var_index < len(sorted_returns) else sorted_returns[-1]
            
            # Calculate CVaR (average of returns below VaR)
            tail_returns = sorted_returns[:var_index] if var_index > 0 else [sorted_returns[0]]
            cvar = np.mean(tail_returns) if len(tail_returns) > 0 else var
            
            results[f'var_{int(cl*100)}'] = float(var)
            results[f'cvar_{int(cl*100)}'] = float(cvar)
        
        return results
    
    def monte_carlo_simulation(self, returns: np.ndarray, 
                             num_simulations: int = 10000) -> Dict[str, float]:
        """
        Monte Carlo simulation for portfolio risk assessment
        
        Args:
            returns: Historical returns
            num_simulations: Number of Monte Carlo simulations
            
        Returns:
            Dictionary with simulated risk metrics
        """
        if len(returns) < 30:
            return {}
        
        # Convert to PyTorch tensor for GPU acceleration
        returns_tensor = torch.tensor(returns, dtype=torch.float32, device=self.device)
        
        # Calculate mean and std
        mean_return = torch.mean(returns_tensor)
        std_return = torch.std(returns_tensor)
        
        # Generate random simulations
        simulated_returns = torch.normal(
            mean=mean_return.repeat(num_simulations),
            std=std_return.repeat(num_simulations)
        )
        
        # Convert back to numpy for analysis
        simulated_returns_np = simulated_returns.cpu().numpy()
        
        # Calculate VaR and CVaR from simulations
        mc_results = self.calculate_var_cvar(simulated_returns_np)
        
        # Add simulation-specific metrics
        mc_results['simulation_mean'] = float(mean_return.cpu())
        mc_results['simulation_std'] = float(std_return.cpu())
        mc_results['num_simulations'] = num_simulations
        
        return mc_results

class RegimeDetector:
    """Market regime detection using Gaussian Mixture Models"""
    
    def __init__(self, n_regimes: int = 3):
        self.n_regimes = n_regimes
        self.regime_labels = ['Bear Market', 'Sideways Market', 'Bull Market']
        self.model = None
        self.scaler = StandardScaler()
        self.is_fitted = False
        
    def prepare_features(self, price_data: pd.DataFrame) -> np.ndarray:
        """
        Prepare features for regime detection
        
        Args:
            price_data: DataFrame with OHLCV data
            
        Returns:
            Feature matrix for regime detection
        """
        # Calculate returns
        price_data['returns'] = price_data['close'].pct_change()
        
        # Calculate volatility (rolling 20-day)
        price_data['volatility'] = price_data['returns'].rolling(20).std()
        
        # Calculate momentum indicators
        price_data['sma_20'] = price_data['close'].rolling(20).mean()
        price_data['sma_50'] = price_data['close'].rolling(50).mean()
        price_data['momentum'] = (price_data['close'] / price_data['sma_20'] - 1)
        
        # Calculate volume indicators
        price_data['volume_sma'] = price_data['volume'].rolling(20).mean()
        price_data['volume_ratio'] = price_data['volume'] / price_data['volume_sma']
        
        # Select features for regime detection
        features = [
            'returns',
            'volatility', 
            'momentum',
            'volume_ratio'
        ]
        
        # Create feature matrix
        feature_matrix = price_data[features].dropna().values
        
        return feature_matrix
    
    def fit(self, price_data: pd.DataFrame) -> None:
        """
        Fit the regime detection model
        
        Args:
            price_data: Historical price data
        """
        try:
            # Prepare features
            features = self.prepare_features(price_data)
            
            if len(features) < 100:
                logger.warning("Insufficient data for regime detection model fitting")
                return
            
            # Scale features
            features_scaled = self.scaler.fit_transform(features)
            
            # Fit Gaussian Mixture Model
            self.model = GaussianMixture(
                n_components=self.n_regimes,
                covariance_type='full',
                max_iter=100,
                random_state=42
            )
            
            self.model.fit(features_scaled)
            self.is_fitted = True
            
            logger.info(f"Regime detection model fitted with {len(features)} data points")
            
        except Exception as e:
            logger.error(f"Error fitting regime detection model: {e}")
            self.is_fitted = False
    
    def predict_regime(self, recent_data: pd.DataFrame) -> RegimeDetectionResult:
        """
        Predict current market regime
        
        Args:
            recent_data: Recent price data for prediction
            
        Returns:
            RegimeDetectionResult with current regime and probabilities
        """
        if not self.is_fitted or self.model is None:
            return RegimeDetectionResult(
                timestamp=datetime.utcnow(),
                current_regime=1,  # Default to sideways
                regime_probabilities=[0.33, 0.34, 0.33],
                regime_labels=self.regime_labels,
                confidence=0.0
            )
        
        try:
            # Prepare features for the most recent period
            features = self.prepare_features(recent_data)
            
            if len(features) == 0:
                raise ValueError("No valid features for regime prediction")
            
            # Use the most recent feature vector
            latest_features = features[-1:].reshape(1, -1)
            features_scaled = self.scaler.transform(latest_features)
            
            # Predict regime probabilities
            regime_probs = self.model.predict_proba(features_scaled)[0]
            current_regime = np.argmax(regime_probs)
            confidence = float(regime_probs[current_regime])
            
            return RegimeDetectionResult(
                timestamp=datetime.utcnow(),
                current_regime=int(current_regime),
                regime_probabilities=regime_probs.tolist(),
                regime_labels=self.regime_labels,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Error predicting regime: {e}")
            return RegimeDetectionResult(
                timestamp=datetime.utcnow(),
                current_regime=1,
                regime_probabilities=[0.33, 0.34, 0.33],
                regime_labels=self.regime_labels,
                confidence=0.0
            )

class PortfolioRiskAnalyzer:
    """Comprehensive portfolio risk analysis"""
    
    def __init__(self):
        self.cvar_calculator = CVaRCalculator()
        self.regime_detector = RegimeDetector()
        self.storage = MarketDataStorage()
        
    async def analyze_symbol_risk(self, symbol: str, 
                                lookback_days: int = 252) -> RiskMetrics:
        """
        Comprehensive risk analysis for a single symbol
        
        Args:
            symbol: Stock symbol to analyze
            lookback_days: Number of days of historical data to use
            
        Returns:
            RiskMetrics with comprehensive risk measures
        """
        try:
            # Get historical data
            historical_data = await self.storage.get_latest_data(symbol, limit=lookback_days)
            
            if len(historical_data) < 30:
                logger.warning(f"Insufficient data for {symbol} risk analysis")
                return self._default_risk_metrics(symbol)
            
            # Convert to DataFrame
            df = pd.DataFrame(historical_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.sort_values('timestamp')
            
            # Calculate returns
            df['returns'] = df['close'].pct_change().dropna()
            returns = df['returns'].dropna().values
            
            if len(returns) < 30:
                return self._default_risk_metrics(symbol)
            
            # Calculate VaR and CVaR
            var_cvar_results = self.cvar_calculator.calculate_var_cvar(returns)
            
            # Calculate additional risk metrics
            volatility = float(np.std(returns) * np.sqrt(252))  # Annualized
            mean_return = float(np.mean(returns) * 252)  # Annualized
            sharpe_ratio = mean_return / volatility if volatility > 0 else 0.0
            
            # Calculate maximum drawdown
            cumulative_returns = (1 + df['returns']).cumprod()
            rolling_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = float(drawdown.min())
            
            return RiskMetrics(
                symbol=symbol,
                timestamp=datetime.utcnow(),
                var_95=var_cvar_results.get('var_95', 0.0),
                var_99=var_cvar_results.get('var_99', 0.0),
                cvar_95=var_cvar_results.get('cvar_95', 0.0),
                cvar_99=var_cvar_results.get('cvar_99', 0.0),
                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown
            )
            
        except Exception as e:
            logger.error(f"Error analyzing risk for {symbol}: {e}")
            return self._default_risk_metrics(symbol)
    
    def _default_risk_metrics(self, symbol: str) -> RiskMetrics:
        """Return default risk metrics when analysis fails"""
        return RiskMetrics(
            symbol=symbol,
            timestamp=datetime.utcnow(),
            var_95=0.0,
            var_99=0.0,
            cvar_95=0.0,
            cvar_99=0.0,
            volatility=0.0,
            sharpe_ratio=0.0,
            max_drawdown=0.0
        )
    
    async def detect_market_regime(self, symbol: str = 'SPY', 
                                 lookback_days: int = 500) -> RegimeDetectionResult:
        """
        Detect current market regime using market index data
        
        Args:
            symbol: Market index symbol (default: SPY)
            lookback_days: Historical data for model training
            
        Returns:
            RegimeDetectionResult with current market regime
        """
        try:
            # Get historical data for regime detection
            historical_data = await self.storage.get_latest_data(symbol, limit=lookback_days)
            
            if len(historical_data) < 100:
                logger.warning(f"Insufficient data for regime detection")
                return RegimeDetectionResult(
                    timestamp=datetime.utcnow(),
                    current_regime=1,
                    regime_probabilities=[0.33, 0.34, 0.33],
                    regime_labels=['Bear Market', 'Sideways Market', 'Bull Market'],
                    confidence=0.0
                )
            
            # Convert to DataFrame
            df = pd.DataFrame(historical_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.sort_values('timestamp')
            
            # Fit regime detection model
            self.regime_detector.fit(df)
            
            # Predict current regime using recent data
            recent_data = df.tail(100)  # Use last 100 days for prediction
            regime_result = self.regime_detector.predict_regime(recent_data)
            
            return regime_result
            
        except Exception as e:
            logger.error(f"Error in regime detection: {e}")
            return RegimeDetectionResult(
                timestamp=datetime.utcnow(),
                current_regime=1,
                regime_probabilities=[0.33, 0.34, 0.33],
                regime_labels=['Bear Market', 'Sideways Market', 'Bull Market'],
                confidence=0.0
            )
    
    async def portfolio_stress_test(self, symbols: List[str], 
                                  weights: Optional[List[float]] = None,
                                  stress_scenarios: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """
        Perform portfolio stress testing
        
        Args:
            symbols: List of portfolio symbols
            weights: Portfolio weights (equal weight if None)
            stress_scenarios: Custom stress scenarios
            
        Returns:
            Dictionary with stress test results
        """
        if weights is None:
            weights = [1.0 / len(symbols)] * len(symbols)
        
        if stress_scenarios is None:
            stress_scenarios = {
                'market_crash': -0.20,  # 20% market decline
                'volatility_spike': 2.0,  # 2x volatility increase
                'correlation_breakdown': 0.9  # High correlation scenario
            }
        
        try:
            # Get risk metrics for each symbol
            risk_metrics = []
            for symbol in symbols:
                metrics = await self.analyze_symbol_risk(symbol)
                risk_metrics.append(metrics)
            
            # Calculate portfolio-level metrics
            portfolio_var_95 = sum(w * rm.var_95 for w, rm in zip(weights, risk_metrics))
            portfolio_cvar_95 = sum(w * rm.cvar_95 for w, rm in zip(weights, risk_metrics))
            portfolio_volatility = np.sqrt(sum((w * rm.volatility) ** 2 for w, rm in zip(weights, risk_metrics)))
            
            # Stress test scenarios
            stress_results = {}
            
            for scenario_name, stress_factor in stress_scenarios.items():
                if scenario_name == 'market_crash':
                    # Apply uniform shock to all positions
                    stressed_var = portfolio_var_95 + stress_factor
                    stressed_cvar = portfolio_cvar_95 + stress_factor
                elif scenario_name == 'volatility_spike':
                    # Increase volatility by stress factor
                    stressed_var = portfolio_var_95 * stress_factor
                    stressed_cvar = portfolio_cvar_95 * stress_factor
                else:
                    # Default stress scenario
                    stressed_var = portfolio_var_95 * (1 + stress_factor)
                    stressed_cvar = portfolio_cvar_95 * (1 + stress_factor)
                
                stress_results[scenario_name] = {
                    'stressed_var_95': float(stressed_var),
                    'stressed_cvar_95': float(stressed_cvar),
                    'stress_factor': stress_factor
                }
            
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'portfolio_symbols': symbols,
                'portfolio_weights': weights,
                'base_metrics': {
                    'portfolio_var_95': float(portfolio_var_95),
                    'portfolio_cvar_95': float(portfolio_cvar_95),
                    'portfolio_volatility': float(portfolio_volatility)
                },
                'stress_scenarios': stress_results,
                'individual_metrics': [rm.to_dict() for rm in risk_metrics]
            }
            
        except Exception as e:
            logger.error(f"Error in portfolio stress testing: {e}")
            return {'error': str(e)}

# Global risk analyzer instance
risk_analyzer = PortfolioRiskAnalyzer()

async def initialize_risk_analytics():
    """Initialize the risk analytics system"""
    logger.info("Risk analytics system initialized with PyTorch MPS acceleration")
