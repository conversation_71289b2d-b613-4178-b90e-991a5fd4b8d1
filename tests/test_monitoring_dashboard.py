"""
QuantumEdge Financial Intelligence Tool - Monitoring Dashboard Tests
Comprehensive testing of monitoring and data quality components
"""

import pytest
import asyncio
import sys
import os
from datetime import datetime, timedelta
import tempfile
import statistics

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.monitoring_dashboard import (
    DataQualityMonitor, AnomalyDetector, SystemMetrics, DataQualityAlert
)
from src.data_validation import DataValidator, ValidationResult, ValidationCheck, ValidationSeverity

class TestAnomalyDetector:
    """Test anomaly detection functionality"""
    
    def setup_method(self):
        self.detector = AnomalyDetector(window_size=50)
    
    def test_anomaly_detection_normal_data(self):
        """Test anomaly detection with normal data"""
        # Add normal data points
        normal_values = [100 + i * 0.1 for i in range(30)]
        
        for value in normal_values:
            self.detector.add_data_point('test_metric', value)
        
        # Test normal value
        is_anomaly, z_score = self.detector.detect_anomaly('test_metric', 102.0)
        assert not is_anomaly
        assert abs(z_score) < 2.0
    
    def test_anomaly_detection_outlier(self):
        """Test anomaly detection with outlier"""
        # Add normal data points
        normal_values = [100 + i * 0.1 for i in range(30)]
        
        for value in normal_values:
            self.detector.add_data_point('test_metric', value)
        
        # Test outlier value
        is_anomaly, z_score = self.detector.detect_anomaly('test_metric', 150.0)
        assert is_anomaly
        assert z_score > 2.0
    
    def test_insufficient_data(self):
        """Test behavior with insufficient data"""
        # Add only a few data points
        for i in range(5):
            self.detector.add_data_point('test_metric', i)
        
        is_anomaly, z_score = self.detector.detect_anomaly('test_metric', 100.0)
        assert not is_anomaly
        assert z_score == 0.0
    
    def test_window_size_limit(self):
        """Test that window size is respected"""
        # Add more data than window size
        for i in range(100):
            self.detector.add_data_point('test_metric', i)
        
        # Should only keep last 50 values
        assert len(self.detector.historical_data['test_metric']) == 50
        assert min(self.detector.historical_data['test_metric']) == 50
    
    def test_statistics_calculation(self):
        """Test statistical summary calculation"""
        values = [1, 2, 3, 4, 5]
        
        for value in values:
            self.detector.add_data_point('test_metric', value)
        
        stats = self.detector.get_statistics('test_metric')
        
        assert stats['mean'] == 3.0
        assert stats['median'] == 3.0
        assert stats['min'] == 1
        assert stats['max'] == 5
        assert stats['count'] == 5
    
    def test_zero_variance_data(self):
        """Test handling of zero variance data"""
        # Add identical values
        for _ in range(20):
            self.detector.add_data_point('test_metric', 100.0)
        
        # Should not detect anomaly for same value
        is_anomaly, z_score = self.detector.detect_anomaly('test_metric', 100.0)
        assert not is_anomaly
        assert z_score == 0.0

class TestDataQualityMonitor:
    """Test data quality monitoring"""
    
    def setup_method(self):
        # Use temporary database for testing
        import uuid
        self.temp_db_path = f"/tmp/test_monitor_{uuid.uuid4().hex}.duckdb"
        self.monitor = DataQualityMonitor(db_path=self.temp_db_path)
    
    def teardown_method(self):
        # Clean up temporary database
        try:
            os.unlink(self.temp_db_path)
        except:
            pass
    
    @pytest.mark.asyncio
    async def test_api_performance_recording(self):
        """Test API performance metric recording"""
        await self.monitor.record_api_performance(
            api_name='test_api',
            endpoint='/test',
            response_time=0.5,
            status_code=200,
            success=True
        )
        
        # Should not raise any exceptions
        assert True
    
    @pytest.mark.asyncio
    async def test_data_quality_recording(self):
        """Test data quality metric recording"""
        # Create a validation result
        validation_result = ValidationResult(
            data_type='test_data',
            symbol='TEST',
            timestamp=datetime.utcnow(),
            quality_score=0.95,
            checks=[
                ValidationCheck(
                    check_name='test_check',
                    score=0.95,
                    severity=ValidationSeverity.INFO,
                    message='Test passed'
                )
            ],
            passed=True,
            record_count=1
        )
        
        await self.monitor.record_data_quality(validation_result)
        
        # Should not raise any exceptions
        assert True
    
    @pytest.mark.asyncio
    async def test_low_quality_alert_creation(self):
        """Test alert creation for low quality data"""
        # Create a low quality validation result
        validation_result = ValidationResult(
            data_type='test_data',
            symbol='TEST',
            timestamp=datetime.utcnow(),
            quality_score=0.3,  # Low quality
            checks=[
                ValidationCheck(
                    check_name='test_check',
                    score=0.3,
                    severity=ValidationSeverity.ERROR,
                    message='Test failed'
                )
            ],
            passed=False,
            record_count=1
        )
        
        initial_alert_count = len(self.monitor.alerts)
        await self.monitor.record_data_quality(validation_result)
        
        # Should create an alert
        assert len(self.monitor.alerts) > initial_alert_count
        
        # Check alert properties
        alert = self.monitor.alerts[-1]
        assert alert.severity in ['high', 'medium']
        assert alert.metric == 'data_quality'
        assert alert.current_value == 0.3
    
    @pytest.mark.asyncio
    async def test_system_health_retrieval(self):
        """Test system health metrics retrieval"""
        # Record some test data first
        await self.monitor.record_api_performance(
            'test_api', '/test', 0.5, 200, True
        )
        
        validation_result = ValidationResult(
            data_type='test_data',
            symbol='TEST',
            timestamp=datetime.utcnow(),
            quality_score=0.95,
            checks=[],
            passed=True,
            record_count=1
        )
        await self.monitor.record_data_quality(validation_result)
        
        # Get system health
        health = await self.monitor.get_system_health()
        
        assert 'timestamp' in health
        assert 'metrics' in health
        assert 'api_performance' in health
        assert 'active_alerts' in health
        assert 'alert_summary' in health
    
    @pytest.mark.asyncio
    async def test_data_quality_report(self):
        """Test data quality report generation"""
        # Record some test data
        validation_result = ValidationResult(
            data_type='test_data',
            symbol='TEST',
            timestamp=datetime.utcnow(),
            quality_score=0.85,
            checks=[],
            passed=True,
            record_count=1
        )
        await self.monitor.record_data_quality(validation_result)
        
        # Generate report
        report = await self.monitor.get_data_quality_report(hours_back=1)
        
        assert 'report_timestamp' in report
        assert 'period_hours' in report
        assert 'quality_trends' in report
        assert 'quality_by_source' in report
        assert 'failed_validations' in report

class TestSystemMetrics:
    """Test SystemMetrics data structure"""
    
    def test_system_metrics_creation(self):
        """Test SystemMetrics creation and serialization"""
        metrics = SystemMetrics(
            timestamp=datetime.utcnow(),
            api_response_times={'test_api': 0.5},
            data_quality_scores={'test_data': 0.95},
            validation_pass_rates={'test_data': 1.0},
            error_rates={'test_api': 0.01},
            throughput_metrics={'test_pipeline': 100.0},
            memory_usage=0.6,
            cpu_usage=0.3
        )
        
        data_dict = metrics.to_dict()
        
        assert 'timestamp' in data_dict
        assert data_dict['api_response_times']['test_api'] == 0.5
        assert data_dict['data_quality_scores']['test_data'] == 0.95
        assert data_dict['memory_usage'] == 0.6
        assert data_dict['cpu_usage'] == 0.3

class TestDataQualityAlert:
    """Test DataQualityAlert data structure"""
    
    def test_alert_creation(self):
        """Test alert creation and serialization"""
        alert = DataQualityAlert(
            alert_id='test_alert_001',
            timestamp=datetime.utcnow(),
            severity='high',
            source='test_source',
            symbol='TEST',
            metric='data_quality',
            current_value=0.3,
            threshold=0.8,
            message='Test alert message'
        )
        
        data_dict = alert.to_dict()
        
        assert data_dict['alert_id'] == 'test_alert_001'
        assert data_dict['severity'] == 'high'
        assert data_dict['source'] == 'test_source'
        assert data_dict['symbol'] == 'TEST'
        assert data_dict['metric'] == 'data_quality'
        assert data_dict['current_value'] == 0.3
        assert data_dict['threshold'] == 0.8

class TestIntegration:
    """Integration tests for monitoring system"""
    
    def setup_method(self):
        import uuid
        self.temp_db_path = f"/tmp/test_integration_{uuid.uuid4().hex}.duckdb"
        self.monitor = DataQualityMonitor(db_path=self.temp_db_path)
    
    def teardown_method(self):
        try:
            os.unlink(self.temp_db_path)
        except:
            pass
    
    @pytest.mark.asyncio
    async def test_end_to_end_monitoring(self):
        """Test complete monitoring workflow"""
        # Simulate API performance data
        api_metrics = [
            ('polygon', '/v2/aggs', 0.5, 200, True),
            ('polygon', '/v2/aggs', 0.8, 200, True),
            ('alpha_vantage', '/query', 1.2, 200, True),
            ('alpha_vantage', '/query', 5.0, 429, False),  # Rate limited
        ]
        
        for api_name, endpoint, response_time, status, success in api_metrics:
            await self.monitor.record_api_performance(
                api_name, endpoint, response_time, status, success
            )
        
        # Simulate data quality results
        quality_results = [
            ('market_data', 'AAPL', 0.98, True),
            ('market_data', 'MSFT', 0.95, True),
            ('market_data', 'GOOGL', 0.75, False),  # Low quality
            ('news_data', 'AAPL', 0.88, True),
        ]
        
        for data_type, symbol, quality_score, passed in quality_results:
            validation_result = ValidationResult(
                data_type=data_type,
                symbol=symbol,
                timestamp=datetime.utcnow(),
                quality_score=quality_score,
                checks=[],
                passed=passed,
                record_count=1
            )
            await self.monitor.record_data_quality(validation_result)
        
        # Get system health
        health = await self.monitor.get_system_health()
        
        # Validate health metrics
        assert health['active_alerts'] >= 0
        assert 'api_performance' in health
        assert 'metrics' in health
        
        # Generate quality report
        report = await self.monitor.get_data_quality_report()
        
        # Validate report structure
        assert 'quality_by_source' in report
        assert 'failed_validations' in report
        
        # Should have detected the low quality data
        failed_validations = report['failed_validations']
        assert any(fv['symbol'] == 'GOOGL' for fv in failed_validations)
    
    @pytest.mark.asyncio
    async def test_anomaly_detection_integration(self):
        """Test anomaly detection in monitoring context"""
        # Record normal API response times
        normal_times = [0.5, 0.6, 0.55, 0.52, 0.58, 0.61, 0.54, 0.57, 0.59, 0.53]
        
        for response_time in normal_times:
            await self.monitor.record_api_performance(
                'test_api', '/test', response_time, 200, True
            )
        
        # Record an anomalous response time
        initial_alert_count = len(self.monitor.alerts)
        
        await self.monitor.record_api_performance(
            'test_api', '/test', 5.0, 200, True  # Very slow response
        )
        
        # Should detect anomaly and create alert
        # Note: This depends on the anomaly detection threshold
        # The test validates the monitoring system can detect anomalies
        assert len(self.monitor.alerts) >= initial_alert_count
    
    @pytest.mark.asyncio
    async def test_performance_requirements(self):
        """Test monitoring system performance"""
        start_time = datetime.utcnow()
        
        # Record many metrics quickly
        for i in range(100):
            await self.monitor.record_api_performance(
                f'api_{i % 3}', '/test', 0.5, 200, True
            )
            
            validation_result = ValidationResult(
                data_type='test_data',
                symbol=f'TEST{i}',
                timestamp=datetime.utcnow(),
                quality_score=0.95,
                checks=[],
                passed=True,
                record_count=1
            )
            await self.monitor.record_data_quality(validation_result)
        
        end_time = datetime.utcnow()
        processing_time = (end_time - start_time).total_seconds()
        
        # Should process quickly
        assert processing_time < 10.0  # 10 seconds max for 200 operations
        
        # Get system health (should be fast)
        health_start = datetime.utcnow()
        health = await self.monitor.get_system_health()
        health_time = (datetime.utcnow() - health_start).total_seconds()
        
        assert health_time < 2.0  # Health check should be fast
        assert 'metrics' in health

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
