"""
QuantumEdge Financial Intelligence Tool - Rate Limiting System
Redis-based distributed rate limiting with budget tracking
"""

import asyncio
import time
import logging
from typing import Dict, Optional, Tu<PERSON>
from datetime import datetime, timedelta
from dataclasses import dataclass
import redis.asyncio as redis
from src.config import config

logger = logging.getLogger('quantumedge.rate_limiter')

@dataclass
class RateLimitInfo:
    """Rate limit information for an API"""
    api_name: str
    requests_remaining: int
    reset_time: Optional[datetime]
    limit_type: str  # 'per_minute', 'per_day', 'per_second'
    current_usage: int

class RateLimiter:
    """Advanced rate limiting system with Redis backend"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_client = None
        self.redis_url = redis_url
        self.api_limits = self._load_api_limits()
        self.budget_tracker = BudgetTracker()
        
    async def initialize(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.from_url(self.redis_url, decode_responses=True)
            await self.redis_client.ping()
            logger.info("Rate limiter initialized with Redis backend")
        except Exception as e:
            logger.warning(f"Redis not available, using in-memory fallback: {e}")
            self.redis_client = None
    
    def _load_api_limits(self) -> Dict:
        """Load API rate limits from configuration"""
        limits = {}
        for api_name, api_config in config.apis.items():
            limits[api_name] = {
                'per_minute': api_config.rate_limit_per_minute,
                'per_day': api_config.rate_limit_per_day,
                'per_second': api_config.rate_limit_per_second
            }
        return limits
    
    async def acquire(self, api_name: str, cost: float = 0.0) -> Tuple[bool, RateLimitInfo]:
        """
        Acquire rate limit token for API request
        
        Args:
            api_name: Name of the API
            cost: Cost of the request in USD
            
        Returns:
            Tuple of (success, rate_limit_info)
        """
        if api_name not in self.api_limits:
            raise ValueError(f"Unknown API: {api_name}")
        
        # Check budget first
        if not await self.budget_tracker.can_afford(cost):
            logger.warning(f"Budget exceeded, cannot make request to {api_name}")
            return False, self._get_rate_limit_info(api_name, 0, 'budget_exceeded')
        
        # Check rate limits
        api_limits = self.api_limits[api_name]
        
        # Check per-second limit
        if api_limits['per_second']:
            success, info = await self._check_rate_limit(
                api_name, 'per_second', api_limits['per_second'], 1
            )
            if not success:
                return False, info
        
        # Check per-minute limit
        if api_limits['per_minute']:
            success, info = await self._check_rate_limit(
                api_name, 'per_minute', api_limits['per_minute'], 60
            )
            if not success:
                return False, info
        
        # Check per-day limit
        if api_limits['per_day']:
            success, info = await self._check_rate_limit(
                api_name, 'per_day', api_limits['per_day'], 86400
            )
            if not success:
                return False, info
        
        # All checks passed, increment counters and track cost
        await self._increment_usage(api_name)
        await self.budget_tracker.record_expense(api_name, cost)
        
        return True, self._get_rate_limit_info(api_name, 1, 'success')
    
    async def _check_rate_limit(self, api_name: str, limit_type: str, 
                               limit: int, window_seconds: int) -> Tuple[bool, RateLimitInfo]:
        """Check specific rate limit (per_second, per_minute, per_day)"""
        key = f"rate_limit:{api_name}:{limit_type}"
        
        if self.redis_client:
            # Redis-based rate limiting
            current_count = await self.redis_client.get(key)
            current_count = int(current_count) if current_count else 0
            
            if current_count >= limit:
                # Rate limit exceeded
                ttl = await self.redis_client.ttl(key)
                reset_time = datetime.utcnow() + timedelta(seconds=ttl) if ttl > 0 else None
                
                return False, RateLimitInfo(
                    api_name=api_name,
                    requests_remaining=0,
                    reset_time=reset_time,
                    limit_type=limit_type,
                    current_usage=current_count
                )
            
            # Increment counter
            pipe = self.redis_client.pipeline()
            pipe.incr(key)
            pipe.expire(key, window_seconds)
            await pipe.execute()
            
            return True, RateLimitInfo(
                api_name=api_name,
                requests_remaining=limit - current_count - 1,
                reset_time=datetime.utcnow() + timedelta(seconds=window_seconds),
                limit_type=limit_type,
                current_usage=current_count + 1
            )
        else:
            # In-memory fallback (less accurate but functional)
            return True, RateLimitInfo(
                api_name=api_name,
                requests_remaining=limit - 1,
                reset_time=datetime.utcnow() + timedelta(seconds=window_seconds),
                limit_type=limit_type,
                current_usage=1
            )
    
    async def _increment_usage(self, api_name: str):
        """Increment usage counters for all applicable limits"""
        api_limits = self.api_limits[api_name]
        
        for limit_type in ['per_second', 'per_minute', 'per_day']:
            if api_limits[limit_type]:
                key = f"usage:{api_name}:{limit_type}"
                if self.redis_client:
                    await self.redis_client.incr(key)
    
    def _get_rate_limit_info(self, api_name: str, usage: int, status: str) -> RateLimitInfo:
        """Get current rate limit information"""
        api_limits = self.api_limits[api_name]
        
        # Use the most restrictive limit for display
        if api_limits['per_second']:
            limit = api_limits['per_second']
            limit_type = 'per_second'
        elif api_limits['per_minute']:
            limit = api_limits['per_minute']
            limit_type = 'per_minute'
        else:
            limit = api_limits['per_day'] or 1000
            limit_type = 'per_day'
        
        return RateLimitInfo(
            api_name=api_name,
            requests_remaining=max(0, limit - usage),
            reset_time=datetime.utcnow() + timedelta(seconds=60),
            limit_type=limit_type,
            current_usage=usage
        )
    
    async def get_usage_stats(self, api_name: str) -> Dict:
        """Get detailed usage statistics for an API"""
        if not self.redis_client:
            return {'error': 'Redis not available'}
        
        stats = {}
        for limit_type in ['per_second', 'per_minute', 'per_day']:
            key = f"usage:{api_name}:{limit_type}"
            usage = await self.redis_client.get(key)
            stats[limit_type] = int(usage) if usage else 0
        
        return stats
    
    async def reset_limits(self, api_name: str):
        """Reset rate limits for an API (admin function)"""
        if not self.redis_client:
            return
        
        keys_to_delete = []
        for limit_type in ['per_second', 'per_minute', 'per_day']:
            keys_to_delete.extend([
                f"rate_limit:{api_name}:{limit_type}",
                f"usage:{api_name}:{limit_type}"
            ])
        
        if keys_to_delete:
            await self.redis_client.delete(*keys_to_delete)
        
        logger.info(f"Rate limits reset for {api_name}")
    
    async def wait_for_reset(self, api_name: str, limit_type: str) -> float:
        """Calculate wait time until rate limit resets"""
        key = f"rate_limit:{api_name}:{limit_type}"
        
        if self.redis_client:
            ttl = await self.redis_client.ttl(key)
            return max(0, ttl)
        else:
            # Fallback wait times
            wait_times = {
                'per_second': 1,
                'per_minute': 60,
                'per_day': 3600  # Wait 1 hour for daily limits
            }
            return wait_times.get(limit_type, 60)

class BudgetTracker:
    """Track API costs and budget utilization"""
    
    def __init__(self):
        self.monthly_budget = config.budget['monthly_budget']
        self.alert_thresholds = config.budget['alert_thresholds']
        self.cost_per_request = config.budget['cost_per_request']
        
    async def can_afford(self, cost: float) -> bool:
        """Check if we can afford a request"""
        current_spend = await self.get_current_spend()
        return (current_spend + cost) <= self.monthly_budget
    
    async def record_expense(self, api_name: str, cost: float):
        """Record an API expense"""
        if cost <= 0:
            return  # Free APIs
        
        key = f"budget:expense:{datetime.utcnow().strftime('%Y-%m')}"
        
        # This would be implemented with Redis or database
        # For now, we'll use a simple in-memory approach
        logger.info(f"Recorded expense: {api_name} - ${cost:.4f}")
        
        # Check if we've hit any alert thresholds
        current_spend = await self.get_current_spend()
        usage_percentage = current_spend / self.monthly_budget
        
        for threshold in self.alert_thresholds:
            if usage_percentage >= threshold:
                await self._send_budget_alert(usage_percentage, current_spend)
                break
    
    async def get_current_spend(self) -> float:
        """Get current month's spending"""
        # This would query Redis or database for actual spending
        # For now, return 0 since we're using free APIs
        return 0.0
    
    async def _send_budget_alert(self, usage_percentage: float, current_spend: float):
        """Send budget alert when threshold is reached"""
        logger.warning(
            f"Budget alert: {usage_percentage:.1%} of monthly budget used "
            f"(${current_spend:.2f} / ${self.monthly_budget:.2f})"
        )
    
    async def get_budget_status(self) -> Dict:
        """Get comprehensive budget status"""
        current_spend = await self.get_current_spend()
        remaining_budget = self.monthly_budget - current_spend
        usage_percentage = current_spend / self.monthly_budget
        
        return {
            'monthly_budget': self.monthly_budget,
            'current_spend': current_spend,
            'remaining_budget': remaining_budget,
            'usage_percentage': usage_percentage,
            'days_remaining': (datetime.now().replace(day=1, month=datetime.now().month+1) - datetime.now()).days,
            'projected_monthly_spend': current_spend * (30 / datetime.now().day) if datetime.now().day > 0 else 0
        }

class RequestScheduler:
    """Intelligent request scheduling to optimize API usage"""
    
    def __init__(self, rate_limiter: RateLimiter):
        self.rate_limiter = rate_limiter
        self.request_queue = asyncio.Queue()
        self.running = False
    
    async def start(self):
        """Start the request scheduler"""
        self.running = True
        asyncio.create_task(self._process_queue())
        logger.info("Request scheduler started")
    
    async def stop(self):
        """Stop the request scheduler"""
        self.running = False
        logger.info("Request scheduler stopped")
    
    async def schedule_request(self, api_name: str, request_func, *args, **kwargs):
        """Schedule a request for optimal execution"""
        request_item = {
            'api_name': api_name,
            'request_func': request_func,
            'args': args,
            'kwargs': kwargs,
            'timestamp': datetime.utcnow(),
            'priority': kwargs.pop('priority', 1)  # Higher number = higher priority
        }
        
        await self.request_queue.put(request_item)
    
    async def _process_queue(self):
        """Process scheduled requests with rate limiting"""
        while self.running:
            try:
                # Get next request (with timeout to allow checking running status)
                request_item = await asyncio.wait_for(
                    self.request_queue.get(), timeout=1.0
                )
                
                api_name = request_item['api_name']
                
                # Check rate limits
                can_proceed, rate_info = await self.rate_limiter.acquire(api_name)
                
                if can_proceed:
                    # Execute the request
                    try:
                        result = await request_item['request_func'](
                            *request_item['args'], **request_item['kwargs']
                        )
                        logger.debug(f"Executed scheduled request for {api_name}")
                    except Exception as e:
                        logger.error(f"Scheduled request failed for {api_name}: {e}")
                else:
                    # Rate limited, calculate wait time and reschedule
                    wait_time = await self.rate_limiter.wait_for_reset(
                        api_name, rate_info.limit_type
                    )
                    
                    logger.info(f"Rate limited for {api_name}, waiting {wait_time}s")
                    await asyncio.sleep(min(wait_time, 300))  # Max 5 minute wait
                    
                    # Put request back in queue
                    await self.request_queue.put(request_item)
                
            except asyncio.TimeoutError:
                # No requests in queue, continue
                continue
            except Exception as e:
                logger.error(f"Error in request scheduler: {e}")
                await asyncio.sleep(1)

# Global rate limiter instance
rate_limiter = RateLimiter()

async def initialize_rate_limiter():
    """Initialize the global rate limiter"""
    await rate_limiter.initialize()
