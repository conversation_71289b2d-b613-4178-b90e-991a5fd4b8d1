"""
QuantumEdge Financial Intelligence Tool - Production Dashboard
Streamlit-based web interface with real-time analytics and portfolio management
"""

import streamlit as st
import asyncio
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import json
import sys
import os

# Add src to path
sys.path.append(os.path.dirname(__file__))

from market_data_pipeline import MarketDataPipeline
from sentiment_engine import sentiment_engine
from risk_analytics import risk_analyzer
from alternative_data import alternative_data_engine
from monitoring_dashboard import data_quality_monitor

# Configure Streamlit page
st.set_page_config(
    page_title="QuantumEdge Financial Intelligence",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for professional styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin: 0.5rem 0;
    }
    .risk-high { border-left-color: #dc3545; }
    .risk-medium { border-left-color: #ffc107; }
    .risk-low { border-left-color: #28a745; }
    .sidebar-section {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'initialized' not in st.session_state:
    st.session_state.initialized = False
    st.session_state.pipeline = None

@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_sample_data():
    """Get sample data for demonstration"""
    return {
        'symbols': ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA'],
        'market_data': {
            'AAPL': {
                'price': 175.50,
                'change': 2.30,
                'change_pct': 1.33,
                'volume': 45000000
            },
            'MSFT': {
                'price': 378.85,
                'change': -1.25,
                'change_pct': -0.33,
                'volume': 28000000
            },
            'GOOGL': {
                'price': 142.75,
                'change': 0.85,
                'change_pct': 0.60,
                'volume': 32000000
            }
        }
    }

async def initialize_systems():
    """Initialize all backend systems"""
    if not st.session_state.initialized:
        with st.spinner("Initializing QuantumEdge systems..."):
            try:
                # Initialize sentiment engine
                await sentiment_engine.initialize()
                
                # Initialize pipeline
                st.session_state.pipeline = MarketDataPipeline()
                await st.session_state.pipeline.__aenter__()
                
                st.session_state.initialized = True
                st.success("✅ Systems initialized successfully!")
                
            except Exception as e:
                st.error(f"❌ Initialization failed: {e}")
                st.session_state.initialized = False

def main():
    """Main dashboard function"""
    
    # Header
    st.markdown('<h1 class="main-header">📊 QuantumEdge Financial Intelligence</h1>', 
                unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.markdown("## 🎛️ Control Panel")
        
        # System status
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.markdown("### System Status")
        
        if st.session_state.initialized:
            st.success("🟢 Systems Online")
        else:
            st.warning("🟡 Systems Initializing")
            if st.button("Initialize Systems"):
                asyncio.run(initialize_systems())
                st.rerun()
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Navigation
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.markdown("### Navigation")
        
        page = st.selectbox(
            "Select Page",
            ["Portfolio Overview", "Risk Analytics", "Sentiment Analysis", 
             "Alternative Data", "System Monitoring"]
        )
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Symbol input
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.markdown("### Analysis Settings")
        
        symbol = st.text_input("Stock Symbol", value="AAPL", help="Enter stock symbol (e.g., AAPL)")
        symbols_list = st.text_area(
            "Portfolio Symbols", 
            value="AAPL,MSFT,GOOGL,AMZN,TSLA",
            help="Comma-separated list of symbols"
        )
        
        lookback_days = st.slider("Lookback Period (Days)", 30, 365, 252)
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Main content area
    if page == "Portfolio Overview":
        show_portfolio_overview(symbols_list.split(','))
    elif page == "Risk Analytics":
        show_risk_analytics(symbol, lookback_days)
    elif page == "Sentiment Analysis":
        show_sentiment_analysis(symbol)
    elif page == "Alternative Data":
        show_alternative_data(symbol)
    elif page == "System Monitoring":
        show_system_monitoring()

def show_portfolio_overview(symbols):
    """Display portfolio overview page"""
    st.header("📈 Portfolio Overview")
    
    # Get sample data
    data = get_sample_data()
    
    # Portfolio metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Portfolio Value", "$1,250,000", "2.3%")
    
    with col2:
        st.metric("Daily P&L", "+$28,750", "2.3%")
    
    with col3:
        st.metric("Total Return", "15.8%", "1.2%")
    
    with col4:
        st.metric("Sharpe Ratio", "1.45", "0.05")
    
    # Portfolio composition
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("Portfolio Performance")
        
        # Create sample performance chart
        dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
        portfolio_values = [1000000 * (1 + 0.0002 * i + 0.01 * (i % 30 - 15) / 15) 
                          for i in range(len(dates))]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=dates,
            y=portfolio_values,
            mode='lines',
            name='Portfolio Value',
            line=dict(color='#1f77b4', width=2)
        ))
        
        fig.update_layout(
            title="Portfolio Performance (YTD)",
            xaxis_title="Date",
            yaxis_title="Portfolio Value ($)",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("Asset Allocation")
        
        # Portfolio allocation pie chart
        allocation_data = {
            'Symbol': symbols[:5],
            'Weight': [25, 20, 20, 20, 15]
        }
        
        fig = px.pie(
            values=allocation_data['Weight'],
            names=allocation_data['Symbol'],
            title="Current Allocation"
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # Holdings table
    st.subheader("Current Holdings")
    
    holdings_data = []
    for symbol in symbols[:5]:
        if symbol.strip() in data['market_data']:
            stock_data = data['market_data'][symbol.strip()]
            holdings_data.append({
                'Symbol': symbol.strip(),
                'Price': f"${stock_data['price']:.2f}",
                'Change': f"{stock_data['change']:+.2f}",
                'Change %': f"{stock_data['change_pct']:+.2f}%",
                'Volume': f"{stock_data['volume']:,}"
            })
    
    if holdings_data:
        df = pd.DataFrame(holdings_data)
        st.dataframe(df, use_container_width=True)

def show_risk_analytics(symbol, lookback_days):
    """Display risk analytics page"""
    st.header("⚠️ Risk Analytics")
    
    # Risk metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown('<div class="metric-card risk-medium">', unsafe_allow_html=True)
        st.metric("VaR (95%)", "-2.8%", "-0.3%")
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="metric-card risk-high">', unsafe_allow_html=True)
        st.metric("CVaR (95%)", "-4.2%", "-0.5%")
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col3:
        st.markdown('<div class="metric-card risk-medium">', unsafe_allow_html=True)
        st.metric("Volatility", "22.5%", "+1.2%")
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col4:
        st.markdown('<div class="metric-card risk-low">', unsafe_allow_html=True)
        st.metric("Max Drawdown", "-8.7%", "+0.8%")
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Risk charts
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Risk Distribution")
        
        # VaR distribution chart
        import numpy as np
        returns = np.random.normal(-0.001, 0.025, 1000)
        
        fig = go.Figure()
        fig.add_trace(go.Histogram(
            x=returns,
            nbinsx=50,
            name='Return Distribution',
            opacity=0.7
        ))
        
        # Add VaR lines
        var_95 = np.percentile(returns, 5)
        fig.add_vline(x=var_95, line_dash="dash", line_color="red", 
                     annotation_text="VaR 95%")
        
        fig.update_layout(
            title=f"Return Distribution - {symbol}",
            xaxis_title="Daily Returns",
            yaxis_title="Frequency",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("Market Regime Detection")
        
        # Regime detection chart
        regime_data = {
            'Regime': ['Bull Market', 'Sideways Market', 'Bear Market'],
            'Probability': [0.45, 0.35, 0.20]
        }
        
        fig = px.bar(
            x=regime_data['Regime'],
            y=regime_data['Probability'],
            title="Current Market Regime Probabilities",
            color=regime_data['Probability'],
            color_continuous_scale='RdYlGn'
        )
        
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)

def show_sentiment_analysis(symbol):
    """Display sentiment analysis page"""
    st.header("💭 Sentiment Analysis")
    
    # Sentiment metrics
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Overall Sentiment", "0.65", "+0.12")
        st.caption("Positive (0.1 to 1.0)")
    
    with col2:
        st.metric("News Sentiment", "0.72", "+0.08")
        st.caption("Based on recent news")
    
    with col3:
        st.metric("Social Sentiment", "0.58", "+0.16")
        st.caption("Reddit & social media")
    
    # Sentiment timeline
    st.subheader("Sentiment Timeline")
    
    # Create sample sentiment data
    dates = pd.date_range(start=datetime.now() - timedelta(days=30), 
                         end=datetime.now(), freq='D')
    sentiment_scores = [0.5 + 0.3 * np.sin(i * 0.2) + 0.1 * np.random.randn() 
                       for i in range(len(dates))]
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=dates,
        y=sentiment_scores,
        mode='lines+markers',
        name='Sentiment Score',
        line=dict(color='#2E8B57', width=2)
    ))
    
    # Add sentiment zones
    fig.add_hline(y=0.6, line_dash="dash", line_color="green", 
                 annotation_text="Positive Threshold")
    fig.add_hline(y=0.4, line_dash="dash", line_color="red", 
                 annotation_text="Negative Threshold")
    
    fig.update_layout(
        title=f"Sentiment Timeline - {symbol}",
        xaxis_title="Date",
        yaxis_title="Sentiment Score",
        height=400
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # Recent news sentiment
    st.subheader("Recent News Analysis")
    
    news_data = [
        {"Headline": f"{symbol} reports strong Q4 earnings", "Sentiment": 0.85, "Source": "Financial News"},
        {"Headline": f"{symbol} announces new product launch", "Sentiment": 0.72, "Source": "Tech News"},
        {"Headline": f"Analysts upgrade {symbol} price target", "Sentiment": 0.68, "Source": "Analyst Report"}
    ]
    
    df = pd.DataFrame(news_data)
    st.dataframe(df, use_container_width=True)

def show_alternative_data(symbol):
    """Display alternative data page"""
    st.header("🔍 Alternative Data Intelligence")
    
    # Alternative data metrics
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Innovation Score", "0.78", "+0.05")
        st.caption("Patent activity & R&D")
    
    with col2:
        st.metric("SEC Filing Sentiment", "0.62", "+0.18")
        st.caption("10-K/10-Q analysis")
    
    with col3:
        st.metric("Economic Context", "Favorable", "Stable")
        st.caption("Macro indicators")
    
    # Patent analysis
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Patent Portfolio Analysis")
        
        patent_data = {
            'Year': [2020, 2021, 2022, 2023, 2024],
            'Patents Filed': [45, 52, 38, 61, 47]
        }
        
        fig = px.bar(
            x=patent_data['Year'],
            y=patent_data['Patents Filed'],
            title=f"Patent Filing Trend - {symbol}",
            color=patent_data['Patents Filed'],
            color_continuous_scale='Blues'
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("Economic Indicators")
        
        econ_data = {
            'Indicator': ['GDP Growth', 'Unemployment', 'Inflation', 'Interest Rate'],
            'Current': [2.1, 3.7, 3.2, 5.25],
            'Trend': ['↑', '↓', '↓', '→']
        }
        
        df = pd.DataFrame(econ_data)
        st.dataframe(df, use_container_width=True)

def show_system_monitoring():
    """Display system monitoring page"""
    st.header("🖥️ System Monitoring")
    
    # System health metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("System Status", "Healthy", "")
        st.caption("All systems operational")
    
    with col2:
        st.metric("API Response Time", "0.45s", "-0.12s")
        st.caption("Average response time")
    
    with col3:
        st.metric("Data Quality", "98.5%", "+0.3%")
        st.caption("Validation pass rate")
    
    with col4:
        st.metric("Active Alerts", "2", "-1")
        st.caption("System alerts")
    
    # Performance charts
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("API Performance")
        
        # API performance chart
        apis = ['Polygon.io', 'Alpha Vantage', 'SEC API']
        response_times = [0.45, 0.82, 0.38]
        
        fig = px.bar(
            x=apis,
            y=response_times,
            title="API Response Times",
            color=response_times,
            color_continuous_scale='RdYlGn_r'
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("Data Quality Trends")
        
        # Data quality timeline
        dates = pd.date_range(start=datetime.now() - timedelta(days=7), 
                             end=datetime.now(), freq='H')
        quality_scores = [0.95 + 0.05 * np.random.randn() for _ in range(len(dates))]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=dates,
            y=quality_scores,
            mode='lines',
            name='Data Quality Score',
            line=dict(color='#1f77b4', width=2)
        ))
        
        fig.update_layout(
            title="Data Quality (Last 7 Days)",
            xaxis_title="Time",
            yaxis_title="Quality Score",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)

if __name__ == "__main__":
    # Import numpy for charts
    import numpy as np
    
    # Run the dashboard
    main()
