#!/usr/bin/env python3
"""
API Validation Test Suite
Validates all API endpoints, rate limits, and response formats
"""

import os
import time
import json
import asyncio
import aiohttp
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class APITestResult:
    """Data class for API test results"""
    api_name: str
    endpoint: str
    status_code: int
    response_time: float
    data_quality_score: float
    rate_limit_info: Dict
    error_message: Optional[str] = None

class APIValidator:
    """Comprehensive API validation and testing suite"""
    
    def __init__(self):
        self.load_api_keys()
        self.test_results: List[APITestResult] = []
        
    def load_api_keys(self):
        """Load API keys from environment variables"""
        self.api_keys = {
            'polygon': os.getenv('POLYGON_API_KEY'),
            'alpha_vantage': os.getenv('ALPHA_VANTAGE_API_KEY'),
            'finnhub': os.getenv('FINNHUB_API_KEY'),
            'coingecko': os.getenv('COINGECKO_API_KEY'),
            'sec': None  # SEC API is public, no key required
        }
        
        # Validate that required keys are present
        missing_keys = [k for k, v in self.api_keys.items() if v is None and k != 'sec']
        if missing_keys:
            logger.warning(f"Missing API keys: {missing_keys}")
    
    async def test_polygon_api(self) -> APITestResult:
        """Test Polygon.io API endpoints"""
        logger.info("Testing Polygon.io API...")
        
        url = "https://api.polygon.io/v2/aggs/ticker/AAPL/range/1/day/2024-01-01/2024-01-31"
        params = {'apikey': self.api_keys['polygon']}
        
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    response_time = time.time() - start_time
                    data = await response.json()
                    
                    # Validate response structure
                    data_quality_score = self._validate_polygon_response(data)
                    
                    # Extract rate limit info from headers
                    rate_limit_info = {
                        'requests_remaining': response.headers.get('X-RateLimit-Remaining'),
                        'reset_time': response.headers.get('X-RateLimit-Reset'),
                        'limit': response.headers.get('X-RateLimit-Limit')
                    }
                    
                    return APITestResult(
                        api_name='Polygon.io',
                        endpoint=url,
                        status_code=response.status,
                        response_time=response_time,
                        data_quality_score=data_quality_score,
                        rate_limit_info=rate_limit_info
                    )
                    
        except Exception as e:
            return APITestResult(
                api_name='Polygon.io',
                endpoint=url,
                status_code=0,
                response_time=time.time() - start_time,
                data_quality_score=0.0,
                rate_limit_info={},
                error_message=str(e)
            )
    
    async def test_alpha_vantage_api(self) -> APITestResult:
        """Test Alpha Vantage API endpoints"""
        logger.info("Testing Alpha Vantage API...")
        
        url = "https://www.alphavantage.co/query"
        params = {
            'function': 'TIME_SERIES_DAILY',
            'symbol': 'AAPL',
            'apikey': self.api_keys['alpha_vantage']
        }
        
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    response_time = time.time() - start_time
                    data = await response.json()
                    
                    # Validate response structure
                    data_quality_score = self._validate_alpha_vantage_response(data)
                    
                    # Alpha Vantage doesn't provide rate limit headers
                    rate_limit_info = {
                        'daily_limit': 25,  # Free tier limit
                        'note': 'Rate limit info not provided in headers'
                    }
                    
                    return APITestResult(
                        api_name='Alpha Vantage',
                        endpoint=url,
                        status_code=response.status,
                        response_time=response_time,
                        data_quality_score=data_quality_score,
                        rate_limit_info=rate_limit_info
                    )
                    
        except Exception as e:
            return APITestResult(
                api_name='Alpha Vantage',
                endpoint=url,
                status_code=0,
                response_time=time.time() - start_time,
                data_quality_score=0.0,
                rate_limit_info={},
                error_message=str(e)
            )
    
    async def test_finnhub_api(self) -> APITestResult:
        """Test Finnhub API endpoints"""
        logger.info("Testing Finnhub API...")
        
        url = "https://finnhub.io/api/v1/company-news"
        params = {
            'symbol': 'AAPL',
            'from': '2024-01-01',
            'to': '2024-01-31',
            'token': self.api_keys['finnhub']
        }
        
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    response_time = time.time() - start_time
                    data = await response.json()
                    
                    # Validate response structure
                    data_quality_score = self._validate_finnhub_response(data)
                    
                    # Extract rate limit info from headers
                    rate_limit_info = {
                        'requests_remaining': response.headers.get('X-RateLimit-Remaining'),
                        'reset_time': response.headers.get('X-RateLimit-Reset'),
                        'limit': response.headers.get('X-RateLimit-Limit')
                    }
                    
                    return APITestResult(
                        api_name='Finnhub',
                        endpoint=url,
                        status_code=response.status,
                        response_time=response_time,
                        data_quality_score=data_quality_score,
                        rate_limit_info=rate_limit_info
                    )
                    
        except Exception as e:
            return APITestResult(
                api_name='Finnhub',
                endpoint=url,
                status_code=0,
                response_time=time.time() - start_time,
                data_quality_score=0.0,
                rate_limit_info={},
                error_message=str(e)
            )
    
    async def test_sec_api(self) -> APITestResult:
        """Test SEC API endpoints"""
        logger.info("Testing SEC API...")
        
        # Test SEC company facts endpoint
        url = "https://data.sec.gov/api/xbrl/companyfacts/CIK0000320193.json"  # Apple's CIK
        headers = {
            'User-Agent': 'QuantumEdge Financial Intelligence Tool (<EMAIL>)'
        }
        
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    response_time = time.time() - start_time
                    data = await response.json()
                    
                    # Validate response structure
                    data_quality_score = self._validate_sec_response(data)
                    
                    # SEC API rate limit is 10 requests per second
                    rate_limit_info = {
                        'requests_per_second': 10,
                        'note': 'SEC enforces 10 requests per second limit'
                    }
                    
                    return APITestResult(
                        api_name='SEC',
                        endpoint=url,
                        status_code=response.status,
                        response_time=response_time,
                        data_quality_score=data_quality_score,
                        rate_limit_info=rate_limit_info
                    )
                    
        except Exception as e:
            return APITestResult(
                api_name='SEC',
                endpoint=url,
                status_code=0,
                response_time=time.time() - start_time,
                data_quality_score=0.0,
                rate_limit_info={},
                error_message=str(e)
            )
    
    def _validate_polygon_response(self, data: Dict) -> float:
        """Validate Polygon.io response structure and data quality"""
        score = 0.0
        
        # Check required fields
        if 'results' in data:
            score += 0.3
            results = data['results']
            if isinstance(results, list) and len(results) > 0:
                score += 0.2
                
                # Check first result structure
                first_result = results[0]
                required_fields = ['o', 'h', 'l', 'c', 'v', 't']  # OHLCV + timestamp
                if all(field in first_result for field in required_fields):
                    score += 0.3
                
                # Validate data types and ranges
                if (isinstance(first_result.get('o'), (int, float)) and 
                    first_result.get('o') > 0):
                    score += 0.2
        
        return min(score, 1.0)
    
    def _validate_alpha_vantage_response(self, data: Dict) -> float:
        """Validate Alpha Vantage response structure and data quality"""
        score = 0.0
        
        # Check for error messages
        if 'Error Message' in data or 'Note' in data:
            return 0.0
        
        # Check required fields
        if 'Time Series (Daily)' in data:
            score += 0.4
            time_series = data['Time Series (Daily)']
            
            if isinstance(time_series, dict) and len(time_series) > 0:
                score += 0.2
                
                # Check first entry structure
                first_date = list(time_series.keys())[0]
                first_entry = time_series[first_date]
                required_fields = ['1. open', '2. high', '3. low', '4. close', '5. volume']
                
                if all(field in first_entry for field in required_fields):
                    score += 0.4
        
        return min(score, 1.0)
    
    def _validate_finnhub_response(self, data: Dict) -> float:
        """Validate Finnhub response structure and data quality"""
        score = 0.0
        
        # Check if response is a list (news articles)
        if isinstance(data, list):
            score += 0.3
            
            if len(data) > 0:
                score += 0.2
                
                # Check first article structure
                first_article = data[0]
                required_fields = ['headline', 'summary', 'url', 'datetime']
                
                if all(field in first_article for field in required_fields):
                    score += 0.3
                
                # Validate data types
                if (isinstance(first_article.get('datetime'), int) and
                    isinstance(first_article.get('headline'), str)):
                    score += 0.2
        
        return min(score, 1.0)
    
    def _validate_sec_response(self, data: Dict) -> float:
        """Validate SEC response structure and data quality"""
        score = 0.0
        
        # Check required fields
        if 'facts' in data:
            score += 0.4
            facts = data['facts']
            
            if isinstance(facts, dict) and len(facts) > 0:
                score += 0.2
                
                # Check for US-GAAP data
                if 'us-gaap' in facts:
                    score += 0.4
        
        return min(score, 1.0)
    
    async def run_all_tests(self) -> List[APITestResult]:
        """Run all API validation tests"""
        logger.info("Starting comprehensive API validation...")
        
        # Run all tests concurrently
        test_tasks = [
            self.test_polygon_api(),
            self.test_alpha_vantage_api(),
            self.test_finnhub_api(),
            self.test_sec_api()
        ]
        
        self.test_results = await asyncio.gather(*test_tasks, return_exceptions=True)
        
        # Filter out exceptions and convert to APITestResult objects
        valid_results = []
        for result in self.test_results:
            if isinstance(result, APITestResult):
                valid_results.append(result)
            else:
                logger.error(f"Test failed with exception: {result}")
        
        self.test_results = valid_results
        return self.test_results
    
    def generate_report(self) -> str:
        """Generate comprehensive validation report"""
        report = []
        report.append("=" * 80)
        report.append("API VALIDATION REPORT")
        report.append("=" * 80)
        report.append(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Total APIs Tested: {len(self.test_results)}")
        report.append("")
        
        for result in self.test_results:
            report.append(f"API: {result.api_name}")
            report.append(f"  Status Code: {result.status_code}")
            report.append(f"  Response Time: {result.response_time:.3f}s")
            report.append(f"  Data Quality Score: {result.data_quality_score:.2f}")
            report.append(f"  Rate Limit Info: {result.rate_limit_info}")
            
            if result.error_message:
                report.append(f"  ERROR: {result.error_message}")
            
            report.append("")
        
        # Summary statistics
        successful_tests = [r for r in self.test_results if r.status_code == 200]
        avg_response_time = sum(r.response_time for r in successful_tests) / len(successful_tests) if successful_tests else 0
        avg_quality_score = sum(r.data_quality_score for r in successful_tests) / len(successful_tests) if successful_tests else 0
        
        report.append("SUMMARY STATISTICS")
        report.append("-" * 40)
        report.append(f"Success Rate: {len(successful_tests)}/{len(self.test_results)} ({len(successful_tests)/len(self.test_results)*100:.1f}%)")
        report.append(f"Average Response Time: {avg_response_time:.3f}s")
        report.append(f"Average Data Quality Score: {avg_quality_score:.2f}")
        report.append("")
        
        return "\n".join(report)

async def main():
    """Main function to run API validation"""
    validator = APIValidator()
    
    # Run all tests
    results = await validator.run_all_tests()
    
    # Generate and print report
    report = validator.generate_report()
    print(report)
    
    # Save report to file
    with open('api_validation_report.txt', 'w') as f:
        f.write(report)
    
    logger.info("API validation complete. Report saved to api_validation_report.txt")

if __name__ == "__main__":
    asyncio.run(main())
