"""
Dynamic Risk Co-Pilot - Reddit Sentiment Analysis Engine
Scrapes r/wallstreetbets and analyzes sentiment using VADER for risk assessment
"""

import praw
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import sqlite3
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
import re
import time
from dataclasses import dataclass

logger = logging.getLogger('risk_copilot.reddit_sentiment')

@dataclass
class SentimentData:
    """Reddit sentiment data structure"""
    date: datetime
    symbol: str
    post_count: int
    avg_sentiment: float
    positive_ratio: float
    negative_ratio: float
    neutral_ratio: float
    compound_score: float
    volume_weighted_sentiment: float
    
    def to_dict(self) -> Dict:
        return {
            'date': self.date.isoformat(),
            'symbol': self.symbol,
            'post_count': self.post_count,
            'avg_sentiment': self.avg_sentiment,
            'positive_ratio': self.positive_ratio,
            'negative_ratio': self.negative_ratio,
            'neutral_ratio': self.neutral_ratio,
            'compound_score': self.compound_score,
            'volume_weighted_sentiment': self.volume_weighted_sentiment
        }

class RedditSentimentEngine:
    """
    Reddit sentiment analysis engine for r/wallstreetbets
    Optimized for M1 Max local processing with budget constraints
    """
    
    def __init__(self, db_path: str = "risk_copilot.db"):
        self.db_path = db_path
        self.reddit = None
        self.analyzer = SentimentIntensityAnalyzer()
        self.watchlist = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'SPY', 'QQQ']
        
        # Stock ticker patterns for extraction
        self.ticker_pattern = re.compile(r'\b[A-Z]{1,5}\b')
        
        # Initialize database
        self._init_database()
        
    def _init_database(self):
        """Initialize SQLite database for sentiment storage"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reddit_sentiment (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    post_count INTEGER,
                    avg_sentiment REAL,
                    positive_ratio REAL,
                    negative_ratio REAL,
                    neutral_ratio REAL,
                    compound_score REAL,
                    volume_weighted_sentiment REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date, symbol)
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reddit_posts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    post_id TEXT UNIQUE,
                    title TEXT,
                    content TEXT,
                    score INTEGER,
                    num_comments INTEGER,
                    created_utc TIMESTAMP,
                    sentiment_compound REAL,
                    tickers_mentioned TEXT,
                    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Reddit sentiment database initialized")
            
        except Exception as e:
            logger.error(f"❌ Error initializing database: {e}")
            raise
    
    def initialize_reddit_client(self, client_id: str = None, client_secret: str = None, 
                                user_agent: str = None):
        """
        Initialize Reddit API client
        For demo purposes, we'll use read-only access without credentials
        """
        try:
            if client_id and client_secret:
                # Use provided credentials
                self.reddit = praw.Reddit(
                    client_id=client_id,
                    client_secret=client_secret,
                    user_agent=user_agent or "RiskCoPilot:v1.0 (by /u/YourUsername)"
                )
            else:
                # For demo: simulate Reddit data instead of requiring API keys
                logger.warning("⚠️ No Reddit credentials provided - using simulated data for demo")
                self.reddit = None
                
            logger.info("✅ Reddit client initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error initializing Reddit client: {e}")
            return False
    
    def extract_tickers(self, text: str) -> List[str]:
        """Extract stock tickers from text"""
        try:
            # Find potential tickers
            potential_tickers = self.ticker_pattern.findall(text.upper())
            
            # Filter for known tickers and common patterns
            valid_tickers = []
            for ticker in potential_tickers:
                # Filter out common false positives
                if (ticker in self.watchlist or 
                    (len(ticker) >= 2 and len(ticker) <= 5 and 
                     ticker not in ['THE', 'AND', 'FOR', 'ARE', 'BUT', 'NOT', 'YOU', 'ALL', 'CAN', 'HER', 'WAS', 'ONE', 'OUR', 'OUT', 'DAY', 'GET', 'HAS', 'HIM', 'HIS', 'HOW', 'ITS', 'NEW', 'NOW', 'OLD', 'SEE', 'TWO', 'WHO', 'BOY', 'DID', 'ITS', 'LET', 'PUT', 'SAY', 'SHE', 'TOO', 'USE'])):
                    valid_tickers.append(ticker)
            
            return list(set(valid_tickers))  # Remove duplicates
            
        except Exception as e:
            logger.error(f"Error extracting tickers: {e}")
            return []
    
    def analyze_text_sentiment(self, text: str) -> Dict[str, float]:
        """Analyze sentiment of text using VADER"""
        try:
            scores = self.analyzer.polarity_scores(text)
            return {
                'compound': scores['compound'],
                'positive': scores['pos'],
                'negative': scores['neg'],
                'neutral': scores['neu']
            }
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return {'compound': 0.0, 'positive': 0.0, 'negative': 0.0, 'neutral': 1.0}
    
    def scrape_wallstreetbets(self, limit: int = 100, time_filter: str = 'day') -> List[Dict]:
        """
        Scrape r/wallstreetbets posts
        For demo without API keys, returns simulated data
        """
        posts_data = []
        
        try:
            if self.reddit is None:
                # Generate simulated Reddit data for demo
                logger.info("📊 Generating simulated Reddit sentiment data for demo")
                return self._generate_simulated_reddit_data(limit)
            
            # Real Reddit scraping (requires API keys)
            subreddit = self.reddit.subreddit('wallstreetbets')
            
            for post in subreddit.hot(limit=limit):
                try:
                    # Combine title and selftext for analysis
                    full_text = f"{post.title} {post.selftext}"
                    
                    # Extract tickers
                    tickers = self.extract_tickers(full_text)
                    
                    # Analyze sentiment
                    sentiment = self.analyze_text_sentiment(full_text)
                    
                    post_data = {
                        'post_id': post.id,
                        'title': post.title,
                        'content': post.selftext[:500],  # Limit content length
                        'score': post.score,
                        'num_comments': post.num_comments,
                        'created_utc': datetime.fromtimestamp(post.created_utc),
                        'sentiment': sentiment,
                        'tickers': tickers
                    }
                    
                    posts_data.append(post_data)
                    
                    # Rate limiting for free tier
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.warning(f"Error processing post {post.id}: {e}")
                    continue
            
            logger.info(f"✅ Scraped {len(posts_data)} posts from r/wallstreetbets")
            return posts_data
            
        except Exception as e:
            logger.error(f"❌ Error scraping wallstreetbets: {e}")
            return self._generate_simulated_reddit_data(limit)
    
    def _generate_simulated_reddit_data(self, limit: int) -> List[Dict]:
        """Generate simulated Reddit data for demo purposes"""
        import random
        
        simulated_posts = []
        
        # Sample post templates with varying sentiment
        post_templates = [
            ("🚀 {ticker} to the moon! Great earnings report", 0.7),
            ("{ticker} looking bullish, strong fundamentals", 0.5),
            ("Worried about {ticker} after recent news", -0.4),
            ("{ticker} might be overvalued, considering puts", -0.6),
            ("Holding {ticker} long term, solid company", 0.3),
            ("{ticker} breaking resistance, time to buy?", 0.2),
            ("Bad news for {ticker}, might dump tomorrow", -0.7),
            ("{ticker} earnings beat expectations! 📈", 0.8),
            ("Market crash incoming, selling {ticker}", -0.8),
            ("{ticker} consolidating, waiting for breakout", 0.1)
        ]
        
        for i in range(min(limit, 50)):  # Limit simulated data
            ticker = random.choice(self.watchlist)
            template, base_sentiment = random.choice(post_templates)
            
            title = template.format(ticker=ticker)
            
            # Add some noise to sentiment
            sentiment_noise = random.uniform(-0.2, 0.2)
            compound_sentiment = max(-1, min(1, base_sentiment + sentiment_noise))
            
            post_data = {
                'post_id': f"sim_{i}_{ticker}",
                'title': title,
                'content': f"Simulated post content about {ticker}",
                'score': random.randint(1, 1000),
                'num_comments': random.randint(0, 200),
                'created_utc': datetime.utcnow() - timedelta(hours=random.randint(0, 24)),
                'sentiment': {
                    'compound': compound_sentiment,
                    'positive': max(0, compound_sentiment),
                    'negative': max(0, -compound_sentiment),
                    'neutral': 1 - abs(compound_sentiment)
                },
                'tickers': [ticker]
            }
            
            simulated_posts.append(post_data)
        
        logger.info(f"📊 Generated {len(simulated_posts)} simulated Reddit posts")
        return simulated_posts
    
    def process_daily_sentiment(self, target_date: datetime = None) -> Dict[str, SentimentData]:
        """Process daily sentiment for all watchlist symbols"""
        if target_date is None:
            target_date = datetime.utcnow().date()
        
        try:
            # Scrape recent posts
            posts = self.scrape_wallstreetbets(limit=100)
            
            # Group by ticker and calculate sentiment metrics
            ticker_sentiment = {}
            
            for ticker in self.watchlist:
                ticker_posts = []
                
                # Find posts mentioning this ticker
                for post in posts:
                    if ticker in post.get('tickers', []):
                        ticker_posts.append(post)
                
                if ticker_posts:
                    # Calculate sentiment metrics
                    sentiments = [post['sentiment']['compound'] for post in ticker_posts]
                    positive_count = len([s for s in sentiments if s > 0.05])
                    negative_count = len([s for s in sentiments if s < -0.05])
                    neutral_count = len(sentiments) - positive_count - negative_count
                    
                    # Volume-weighted sentiment (weight by post score)
                    total_score = sum(post['score'] for post in ticker_posts)
                    if total_score > 0:
                        volume_weighted = sum(
                            post['sentiment']['compound'] * post['score'] 
                            for post in ticker_posts
                        ) / total_score
                    else:
                        volume_weighted = sum(sentiments) / len(sentiments)
                    
                    sentiment_data = SentimentData(
                        date=target_date,
                        symbol=ticker,
                        post_count=len(ticker_posts),
                        avg_sentiment=sum(sentiments) / len(sentiments),
                        positive_ratio=positive_count / len(sentiments),
                        negative_ratio=negative_count / len(sentiments),
                        neutral_ratio=neutral_count / len(sentiments),
                        compound_score=sum(sentiments) / len(sentiments),
                        volume_weighted_sentiment=volume_weighted
                    )
                    
                    ticker_sentiment[ticker] = sentiment_data
                else:
                    # No posts found, neutral sentiment
                    sentiment_data = SentimentData(
                        date=target_date,
                        symbol=ticker,
                        post_count=0,
                        avg_sentiment=0.0,
                        positive_ratio=0.33,
                        negative_ratio=0.33,
                        neutral_ratio=0.34,
                        compound_score=0.0,
                        volume_weighted_sentiment=0.0
                    )
                    
                    ticker_sentiment[ticker] = sentiment_data
            
            # Store in database
            self._store_sentiment_data(ticker_sentiment)
            
            logger.info(f"✅ Processed sentiment for {len(ticker_sentiment)} tickers")
            return ticker_sentiment
            
        except Exception as e:
            logger.error(f"❌ Error processing daily sentiment: {e}")
            return {}
    
    def _store_sentiment_data(self, sentiment_data: Dict[str, SentimentData]):
        """Store sentiment data in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for ticker, data in sentiment_data.items():
                cursor.execute('''
                    INSERT OR REPLACE INTO reddit_sentiment 
                    (date, symbol, post_count, avg_sentiment, positive_ratio, 
                     negative_ratio, neutral_ratio, compound_score, volume_weighted_sentiment)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data.date.isoformat(),
                    data.symbol,
                    data.post_count,
                    data.avg_sentiment,
                    data.positive_ratio,
                    data.negative_ratio,
                    data.neutral_ratio,
                    data.compound_score,
                    data.volume_weighted_sentiment
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error storing sentiment data: {e}")
    
    def get_sentiment_history(self, symbol: str, days_back: int = 30) -> pd.DataFrame:
        """Get historical sentiment data for a symbol"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = '''
                SELECT * FROM reddit_sentiment 
                WHERE symbol = ? 
                ORDER BY date DESC 
                LIMIT ?
            '''
            
            df = pd.read_sql_query(query, conn, params=(symbol, days_back))
            conn.close()
            
            return df
            
        except Exception as e:
            logger.error(f"Error getting sentiment history: {e}")
            return pd.DataFrame()

# Global sentiment engine
reddit_sentiment_engine = RedditSentimentEngine()

async def initialize_reddit_sentiment():
    """Initialize Reddit sentiment engine"""
    reddit_sentiment_engine.initialize_reddit_client()
    logger.info("Reddit sentiment engine initialized")
