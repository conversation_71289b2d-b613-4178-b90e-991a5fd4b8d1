"""
QuantumEdge Financial Intelligence Tool - Risk Analytics Tests
Comprehensive testing of CVaR models, regime detection, and portfolio analysis
"""

import pytest
import asyncio
import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.risk_analytics import (
    CVaRCalculator, RegimeDetector, PortfolioRiskAnalyzer,
    RiskMetrics, RegimeDetectionResult
)

class TestCVaRCalculator:
    """Test CVaR calculation functionality"""
    
    def setup_method(self):
        self.calculator = CVaRCalculator()
    
    def test_var_cvar_calculation(self):
        """Test VaR and CVaR calculation with normal returns"""
        # Generate sample returns (normal distribution)
        np.random.seed(42)
        returns = np.random.normal(0.001, 0.02, 1000)  # Daily returns
        
        results = self.calculator.calculate_var_cvar(returns)
        
        # Check that results contain expected keys
        assert 'var_95' in results
        assert 'var_99' in results
        assert 'cvar_95' in results
        assert 'cvar_99' in results
        
        # VaR and CVaR should be negative (losses)
        assert results['var_95'] < 0
        assert results['var_99'] < 0
        assert results['cvar_95'] < 0
        assert results['cvar_99'] < 0
        
        # CVaR should be more negative than VaR (higher loss)
        assert results['cvar_95'] <= results['var_95']
        assert results['cvar_99'] <= results['var_99']
        
        # 99% VaR should be more negative than 95% VaR
        assert results['var_99'] <= results['var_95']
    
    def test_insufficient_data(self):
        """Test behavior with insufficient data"""
        # Very small dataset
        returns = np.array([0.01, -0.02, 0.005])
        
        results = self.calculator.calculate_var_cvar(returns)
        
        # Should return zeros for insufficient data
        assert all(v == 0.0 for v in results.values())
    
    def test_monte_carlo_simulation(self):
        """Test Monte Carlo simulation functionality"""
        # Generate sample returns
        np.random.seed(42)
        returns = np.random.normal(0.001, 0.02, 252)  # One year of daily returns
        
        mc_results = self.calculator.monte_carlo_simulation(returns, num_simulations=1000)
        
        # Check that results contain expected keys
        assert 'var_95' in mc_results
        assert 'cvar_95' in mc_results
        assert 'simulation_mean' in mc_results
        assert 'simulation_std' in mc_results
        assert 'num_simulations' in mc_results
        
        # Simulation parameters should be reasonable
        assert mc_results['num_simulations'] == 1000
        assert isinstance(mc_results['simulation_mean'], float)
        assert isinstance(mc_results['simulation_std'], float)
    
    def test_extreme_returns(self):
        """Test with extreme return scenarios"""
        # Create returns with extreme values
        returns = np.array([-0.5, -0.3, -0.1, 0.0, 0.1, 0.3, 0.5] * 10)
        
        results = self.calculator.calculate_var_cvar(returns)
        
        # Should handle extreme values without errors
        assert all(isinstance(v, float) for v in results.values())
        assert not any(np.isnan(v) for v in results.values())

class TestRegimeDetector:
    """Test market regime detection"""
    
    def setup_method(self):
        self.detector = RegimeDetector(n_regimes=3)
    
    def create_sample_price_data(self, n_days: int = 500) -> pd.DataFrame:
        """Create sample price data for testing"""
        dates = pd.date_range(start='2023-01-01', periods=n_days, freq='D')
        
        # Generate synthetic price data with different regimes
        np.random.seed(42)
        
        # Bull market phase (first third)
        bull_returns = np.random.normal(0.001, 0.015, n_days // 3)
        
        # Bear market phase (middle third)
        bear_returns = np.random.normal(-0.002, 0.025, n_days // 3)
        
        # Sideways market phase (last third)
        sideways_returns = np.random.normal(0.0, 0.012, n_days - 2 * (n_days // 3))
        
        all_returns = np.concatenate([bull_returns, bear_returns, sideways_returns])
        
        # Generate price series
        initial_price = 100.0
        prices = [initial_price]
        
        for ret in all_returns:
            prices.append(prices[-1] * (1 + ret))
        
        prices = prices[1:]  # Remove initial price
        
        # Create OHLCV data
        data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            # Simple OHLCV generation
            high = price * (1 + abs(all_returns[i]) * 0.5)
            low = price * (1 - abs(all_returns[i]) * 0.5)
            volume = np.random.randint(1000000, 5000000)
            
            data.append({
                'timestamp': date,
                'open': price,
                'high': high,
                'low': low,
                'close': price,
                'volume': volume
            })
        
        return pd.DataFrame(data)
    
    def test_feature_preparation(self):
        """Test feature preparation for regime detection"""
        price_data = self.create_sample_price_data(100)
        
        features = self.detector.prepare_features(price_data)
        
        # Should return a 2D array
        assert features.ndim == 2
        assert features.shape[1] == 4  # 4 features
        assert features.shape[0] > 0  # Should have some valid rows
        
        # Features should not contain NaN values
        assert not np.isnan(features).any()
    
    def test_model_fitting(self):
        """Test regime detection model fitting"""
        price_data = self.create_sample_price_data(200)
        
        # Fit the model
        self.detector.fit(price_data)
        
        # Model should be fitted
        assert self.detector.is_fitted
        assert self.detector.model is not None
    
    def test_regime_prediction(self):
        """Test regime prediction"""
        price_data = self.create_sample_price_data(200)
        
        # Fit the model
        self.detector.fit(price_data)
        
        # Predict regime using recent data
        recent_data = price_data.tail(50)
        result = self.detector.predict_regime(recent_data)
        
        # Check result structure
        assert isinstance(result, RegimeDetectionResult)
        assert result.current_regime in [0, 1, 2]
        assert len(result.regime_probabilities) == 3
        assert len(result.regime_labels) == 3
        assert 0.0 <= result.confidence <= 1.0
        
        # Probabilities should sum to approximately 1
        assert abs(sum(result.regime_probabilities) - 1.0) < 0.01
    
    def test_insufficient_data_handling(self):
        """Test handling of insufficient data"""
        # Very small dataset
        price_data = self.create_sample_price_data(10)
        
        # Should handle gracefully
        self.detector.fit(price_data)
        
        # Model should not be fitted with insufficient data
        assert not self.detector.is_fitted
    
    def test_prediction_without_fitting(self):
        """Test prediction without model fitting"""
        price_data = self.create_sample_price_data(50)
        
        # Try to predict without fitting
        result = self.detector.predict_regime(price_data)
        
        # Should return default result
        assert isinstance(result, RegimeDetectionResult)
        assert result.confidence == 0.0

class TestPortfolioRiskAnalyzer:
    """Test portfolio risk analysis"""
    
    def setup_method(self):
        self.analyzer = PortfolioRiskAnalyzer()
    
    def test_risk_metrics_structure(self):
        """Test RiskMetrics data structure"""
        metrics = RiskMetrics(
            symbol='TEST',
            timestamp=datetime.utcnow(),
            var_95=-0.05,
            var_99=-0.08,
            cvar_95=-0.07,
            cvar_99=-0.12,
            volatility=0.25,
            sharpe_ratio=0.8,
            max_drawdown=-0.15
        )
        
        data_dict = metrics.to_dict()
        
        assert data_dict['symbol'] == 'TEST'
        assert data_dict['var_95'] == -0.05
        assert data_dict['var_99'] == -0.08
        assert data_dict['cvar_95'] == -0.07
        assert data_dict['cvar_99'] == -0.12
        assert data_dict['volatility'] == 0.25
        assert data_dict['sharpe_ratio'] == 0.8
        assert data_dict['max_drawdown'] == -0.15
    
    @pytest.mark.asyncio
    async def test_default_risk_metrics(self):
        """Test default risk metrics generation"""
        # Test with non-existent symbol (should return defaults)
        metrics = self.analyzer._default_risk_metrics('NONEXISTENT')
        
        assert metrics.symbol == 'NONEXISTENT'
        assert metrics.var_95 == 0.0
        assert metrics.var_99 == 0.0
        assert metrics.cvar_95 == 0.0
        assert metrics.cvar_99 == 0.0
        assert metrics.volatility == 0.0
        assert metrics.sharpe_ratio == 0.0
        assert metrics.max_drawdown == 0.0
    
    @pytest.mark.asyncio
    async def test_portfolio_stress_test_structure(self):
        """Test portfolio stress test structure"""
        symbols = ['AAPL', 'MSFT', 'GOOGL']
        weights = [0.4, 0.3, 0.3]
        
        # This will use mock data since we don't have real historical data
        stress_results = await self.analyzer.portfolio_stress_test(symbols, weights)
        
        # Check structure
        assert 'timestamp' in stress_results
        assert 'portfolio_symbols' in stress_results
        assert 'portfolio_weights' in stress_results
        assert 'base_metrics' in stress_results
        assert 'stress_scenarios' in stress_results
        assert 'individual_metrics' in stress_results
        
        # Check that stress scenarios are present
        assert 'market_crash' in stress_results['stress_scenarios']
        assert 'volatility_spike' in stress_results['stress_scenarios']
        
        # Check portfolio composition
        assert stress_results['portfolio_symbols'] == symbols
        assert stress_results['portfolio_weights'] == weights

class TestIntegration:
    """Integration tests for risk analytics"""
    
    @pytest.mark.asyncio
    async def test_risk_analytics_initialization(self):
        """Test risk analytics system initialization"""
        from src.risk_analytics import initialize_risk_analytics
        
        # Should initialize without errors
        await initialize_risk_analytics()
    
    def test_pytorch_mps_availability(self):
        """Test PyTorch MPS availability detection"""
        import torch
        
        calculator = CVaRCalculator()
        
        # Device should be set correctly
        assert calculator.device.type in ['mps', 'cpu']
        
        # If MPS is available, should use it
        if torch.backends.mps.is_available():
            assert calculator.device.type == 'mps'
    
    def test_numerical_stability(self):
        """Test numerical stability with edge cases"""
        calculator = CVaRCalculator()
        
        # Test with zero returns
        zero_returns = np.zeros(100)
        results = calculator.calculate_var_cvar(zero_returns)
        
        # Should handle gracefully
        assert all(v == 0.0 for v in results.values())
        
        # Test with constant returns
        constant_returns = np.ones(100) * 0.01
        results = calculator.calculate_var_cvar(constant_returns)
        
        # Should return the constant value
        assert abs(results['var_95'] - 0.01) < 1e-10
        assert abs(results['cvar_95'] - 0.01) < 1e-10
    
    @pytest.mark.asyncio
    async def test_performance_requirements(self):
        """Test performance requirements for risk calculations"""
        calculator = CVaRCalculator()
        
        # Generate large dataset
        np.random.seed(42)
        large_returns = np.random.normal(0.001, 0.02, 10000)
        
        start_time = datetime.utcnow()
        
        # Calculate VaR/CVaR
        results = calculator.calculate_var_cvar(large_returns)
        
        # Run Monte Carlo simulation
        mc_results = calculator.monte_carlo_simulation(large_returns, num_simulations=5000)
        
        end_time = datetime.utcnow()
        processing_time = (end_time - start_time).total_seconds()
        
        # Should complete within reasonable time
        assert processing_time < 10.0  # 10 seconds max
        
        # Results should be valid
        assert all(isinstance(v, float) for v in results.values())
        assert all(isinstance(v, (int, float)) for v in mc_results.values())

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
