#!/usr/bin/env python3
"""
QuantumEdge Financial Intelligence Tool - Real Simulation Trading Demo
Uses real market data with realistic trade simulation for comprehensive testing
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')

from src.simulation_trading_client import simulation_client
from src.real_backtesting_engine import backtest_engine
from src.alpaca_trading_client import TradingSignal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('quantumedge.simulation_demo')

async def test_simulation_client():
    """Test simulation trading client with real market data"""
    logger.info("🔍 TESTING SIMULATION TRADING CLIENT")
    logger.info("-" * 50)
    
    try:
        # Initialize simulation client
        await simulation_client.initialize()
        
        # Get account info
        account_info = await simulation_client.get_account_info()
        
        if 'error' in account_info:
            logger.error(f"❌ Simulation client error: {account_info['error']}")
            return False
        
        logger.info("✅ Simulation client initialized successfully!")
        logger.info(f"📊 Account Number: {account_info['account_number']}")
        logger.info(f"💰 Starting Equity: ${account_info['equity']:,.2f}")
        logger.info(f"💵 Available Cash: ${account_info['cash']:,.2f}")
        logger.info(f"🔄 Buying Power: ${account_info['buying_power']:,.2f}")
        logger.info(f"📊 Current Positions: {account_info['positions_count']}")
        
        # Test real market data access
        logger.info("\n📈 Testing real market data access...")
        test_symbols = ['AAPL', 'MSFT', 'GOOGL']
        
        for symbol in test_symbols:
            price = await simulation_client.get_current_price(symbol)
            if price:
                logger.info(f"   {symbol}: ${price:.2f}")
            else:
                logger.warning(f"   {symbol}: Price not available")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Simulation client test failed: {e}")
        return False

async def test_simulated_trading():
    """Test simulated trade execution with real market data"""
    logger.info("\n🚀 TESTING SIMULATED TRADE EXECUTION")
    logger.info("-" * 50)
    
    try:
        # Create test trading signals
        test_signals = [
            TradingSignal(
                symbol='AAPL',
                action='BUY',
                confidence=0.85,
                sentiment_score=0.75,
                risk_score=0.03,
                regime_confidence=0.80,
                position_size=400.0
            ),
            TradingSignal(
                symbol='MSFT',
                action='BUY',
                confidence=0.78,
                sentiment_score=0.70,
                risk_score=0.04,
                regime_confidence=0.75,
                position_size=350.0
            )
        ]
        
        logger.info(f"📊 Executing {len(test_signals)} test trading signals...")
        
        executions = []
        for signal in test_signals:
            logger.info(f"\n🎯 Executing signal: {signal.action} {signal.symbol}")
            logger.info(f"   Confidence: {signal.confidence:.3f}")
            logger.info(f"   Position Size: ${signal.position_size:.2f}")
            
            execution = await simulation_client.execute_trade(signal)
            executions.append(execution)
            
            if execution.status == 'FILLED':
                logger.info(f"✅ Trade executed successfully!")
                logger.info(f"   Order ID: {execution.order_id}")
                logger.info(f"   Execution Price: ${execution.execution_price:.2f}")
                logger.info(f"   Execution Time: {execution.execution_time.strftime('%H:%M:%S')}")
            else:
                logger.warning(f"⚠️ Trade failed: {execution.error_message}")
        
        # Check updated account status
        logger.info("\n📊 Updated Account Status:")
        account_info = await simulation_client.get_account_info()
        logger.info(f"   Cash: ${account_info['cash']:,.2f}")
        logger.info(f"   Equity: ${account_info['equity']:,.2f}")
        logger.info(f"   Positions: {account_info['positions_count']}")
        
        if account_info['positions']:
            logger.info("   Position Details:")
            for pos in account_info['positions']:
                pnl_indicator = "📈" if pos['unrealized_pl'] >= 0 else "📉"
                logger.info(f"     {pnl_indicator} {pos['symbol']}: {pos['qty']:.3f} shares, "
                           f"${pos['market_value']:,.2f} value, "
                           f"${pos['unrealized_pl']:,.2f} P&L")
        
        return executions
        
    except Exception as e:
        logger.error(f"❌ Simulated trading test failed: {e}")
        return []

async def run_real_data_backtest():
    """Run backtest using real historical market data"""
    logger.info("\n🔄 RUNNING REAL DATA BACKTEST")
    logger.info("-" * 50)
    
    try:
        # Use yfinance for historical data in backtest
        import yfinance as yf
        
        # Override the backtest engine's data method to use yfinance
        async def get_yfinance_data(symbol: str, start_date: datetime, end_date: datetime):
            try:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(
                    start=start_date.strftime('%Y-%m-%d'),
                    end=end_date.strftime('%Y-%m-%d')
                )
                
                if hist.empty:
                    return pd.DataFrame()
                
                # Convert to expected format
                data = []
                for date, row in hist.iterrows():
                    data.append({
                        'timestamp': date,
                        'open': float(row['Open']),
                        'high': float(row['High']),
                        'low': float(row['Low']),
                        'close': float(row['Close']),
                        'volume': int(row['Volume'])
                    })
                
                import pandas as pd
                df = pd.DataFrame(data)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                return df.sort_values('timestamp')
                
            except Exception as e:
                logger.error(f"Error getting yfinance data for {symbol}: {e}")
                return pd.DataFrame()
        
        # Replace the method temporarily
        original_method = backtest_engine.get_historical_data
        backtest_engine.get_historical_data = get_yfinance_data
        
        # Run backtest
        symbols = ['AAPL', 'MSFT', 'GOOGL']
        end_date = datetime.utcnow() - timedelta(days=1)
        start_date = end_date - timedelta(days=60)  # 2 months
        
        logger.info(f"📊 Backtesting {len(symbols)} symbols over {(end_date - start_date).days} days")
        logger.info(f"📅 Period: {start_date.date()} to {end_date.date()}")
        
        results = await backtest_engine.run_backtest(symbols, start_date, end_date)
        
        # Restore original method
        backtest_engine.get_historical_data = original_method
        
        # Display results
        logger.info("\n📊 BACKTEST RESULTS SUMMARY")
        logger.info("=" * 50)
        logger.info(f"💰 Initial Capital: ${results.initial_capital:,.2f}")
        logger.info(f"💰 Final Capital: ${results.final_capital:,.2f}")
        logger.info(f"📈 Total Return: ${results.total_return:,.2f} ({results.total_return_pct:+.2f}%)")
        logger.info(f"⚡ Sharpe Ratio: {results.sharpe_ratio:.2f}")
        logger.info(f"📉 Max Drawdown: {results.max_drawdown:.2f}%")
        logger.info(f"🎯 Win Rate: {results.win_rate:.1f}%")
        logger.info(f"📊 Total Trades: {results.total_trades}")
        
        # Performance assessment
        logger.info(f"\n🎯 PERFORMANCE ASSESSMENT:")
        
        performance_score = 0
        max_score = 5
        
        if results.total_return_pct > 0:
            logger.info("✅ Strategy was profitable (+1 point)")
            performance_score += 1
        else:
            logger.info("❌ Strategy was unprofitable (0 points)")
        
        if results.win_rate >= 60:
            logger.info("✅ Win rate meets 60% target (+1 point)")
            performance_score += 1
        else:
            logger.info(f"⚠️ Win rate ({results.win_rate:.1f}%) below 60% target (0 points)")
        
        if results.sharpe_ratio >= 1.0:
            logger.info("✅ Sharpe ratio meets 1.0+ target (+1 point)")
            performance_score += 1
        else:
            logger.info(f"⚠️ Sharpe ratio ({results.sharpe_ratio:.2f}) below 1.0 target (0 points)")
        
        if results.max_drawdown <= 10:
            logger.info("✅ Max drawdown within 10% limit (+1 point)")
            performance_score += 1
        else:
            logger.info(f"⚠️ Max drawdown ({results.max_drawdown:.2f}%) exceeds 10% limit (0 points)")
        
        if results.total_trades >= 5:
            logger.info("✅ Sufficient trading activity (+1 point)")
            performance_score += 1
        else:
            logger.info(f"⚠️ Low trading activity ({results.total_trades} trades) (0 points)")
        
        logger.info(f"\n🏆 OVERALL SCORE: {performance_score}/{max_score}")
        
        if performance_score >= 4:
            logger.info("🎉 EXCELLENT - Strategy ready for live paper trading!")
        elif performance_score >= 3:
            logger.info("✅ GOOD - Strategy shows promise, minor adjustments recommended")
        elif performance_score >= 2:
            logger.info("⚠️ FAIR - Strategy needs improvement before live deployment")
        else:
            logger.info("❌ POOR - Strategy requires significant refinement")
        
        # Save results
        results_file = f"simulation_backtest_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(results.to_dict(), f, indent=2, default=str)
        
        logger.info(f"\n📄 Detailed results saved to: {results_file}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Real data backtest failed: {e}")
        return None

async def test_portfolio_monitoring():
    """Test portfolio performance monitoring"""
    logger.info("\n📈 TESTING PORTFOLIO MONITORING")
    logger.info("-" * 50)
    
    try:
        # Get portfolio performance
        performance = await simulation_client.get_portfolio_performance()
        
        if 'error' in performance:
            logger.error(f"❌ Portfolio monitoring error: {performance['error']}")
            return False
        
        logger.info("✅ Portfolio monitoring active")
        logger.info(f"📊 Portfolio Performance:")
        logger.info(f"   Total Equity: ${performance['total_equity']:,.2f}")
        logger.info(f"   Total P&L: ${performance['total_pl']:,.2f} ({performance['total_pl_pct']:+.2f}%)")
        logger.info(f"   Active Positions: {performance['total_positions']}")
        logger.info(f"   Win Rate: {performance['win_rate']:.1f}%")
        logger.info(f"   Trades Executed: {performance['trades_executed']}")
        
        # Account status
        account_status = performance['account_status']
        logger.info(f"\n💰 Account Status:")
        logger.info(f"   Cash: ${account_status['cash']:,.2f}")
        logger.info(f"   Buying Power: ${account_status['buying_power']:,.2f}")
        logger.info(f"   Portfolio Value: ${account_status['portfolio_value']:,.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Portfolio monitoring test failed: {e}")
        return False

async def main():
    """Main demo function"""
    logger.info("🚀 QUANTUMEDGE REAL SIMULATION TRADING DEMO")
    logger.info("=" * 80)
    logger.info("Using REAL market data with realistic trade simulation")
    logger.info("This provides comprehensive testing without requiring Alpaca paper credentials")
    
    # Test results tracking
    test_results = {
        'simulation_client': False,
        'simulated_trading': False,
        'real_data_backtest': False,
        'portfolio_monitoring': False
    }
    
    try:
        # 1. Test simulation client
        logger.info("\n🔧 PHASE 1: SIMULATION CLIENT TEST")
        test_results['simulation_client'] = await test_simulation_client()
        
        if not test_results['simulation_client']:
            logger.error("❌ Cannot proceed without simulation client")
            return
        
        # 2. Test simulated trading
        logger.info("\n🔧 PHASE 2: SIMULATED TRADING TEST")
        executions = await test_simulated_trading()
        test_results['simulated_trading'] = len(executions) > 0
        
        # 3. Run real data backtest
        logger.info("\n🔧 PHASE 3: REAL DATA BACKTESTING")
        backtest_results = await run_real_data_backtest()
        test_results['real_data_backtest'] = backtest_results is not None
        
        # 4. Test portfolio monitoring
        logger.info("\n🔧 PHASE 4: PORTFOLIO MONITORING")
        test_results['portfolio_monitoring'] = await test_portfolio_monitoring()
        
        # Final summary
        logger.info("\n🎉 SIMULATION DEMO COMPLETION SUMMARY")
        logger.info("=" * 80)
        
        for test_name, result in test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
        
        all_passed = all(test_results.values())
        
        if all_passed:
            logger.info("\n🎯 ALL TESTS PASSED - SIMULATION SYSTEM FULLY OPERATIONAL")
            logger.info("\n📊 SYSTEM CAPABILITIES VALIDATED:")
            logger.info("✅ Real market data access via yfinance")
            logger.info("✅ Realistic trade execution simulation")
            logger.info("✅ Historical backtesting with real data")
            logger.info("✅ Portfolio performance monitoring")
            logger.info("✅ Risk management and position sizing")
            logger.info("✅ Comprehensive performance metrics")
            
            logger.info("\n🎯 NEXT STEPS:")
            logger.info("1. ✅ System is ready for continuous testing")
            logger.info("2. 🔄 Run daily simulations to validate strategy")
            logger.info("3. 📊 Monitor performance metrics and adjust parameters")
            logger.info("4. 🚀 When ready, migrate to real Alpaca paper trading")
            logger.info("5. 📈 Scale position sizes based on proven performance")
            
            logger.info("\n🔧 TO ENABLE REAL ALPACA PAPER TRADING:")
            logger.info("1. Generate proper paper trading API credentials from Alpaca")
            logger.info("2. Update .env file with paper trading keys")
            logger.info("3. Replace simulation_client with alpaca_client in live system")
            
        else:
            logger.info("\n⚠️ SOME TESTS FAILED - REVIEW AND FIX ISSUES")
            logger.info("🔧 Address failed components before proceeding")
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ Demo interrupted by user")
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        raise

if __name__ == "__main__":
    # Run the real simulation trading demo
    asyncio.run(main())
