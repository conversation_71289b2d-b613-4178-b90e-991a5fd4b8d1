# 🚀 **ALPACA PAPER TRADING SETUP GUIDE**

## **CRITICAL: Paper Trading Requires Separate API Keys**

The current API keys in your `.env` file appear to be for **live trading**, not paper trading. Alpaca requires **separate API credentials** for paper trading accounts.

## **📋 STEP-BY-STEP SETUP PROCESS**

### **1. Access Your Alpaca Dashboard**
- Go to [https://app.alpaca.markets](https://app.alpaca.markets)
- Log in to your Alpaca account

### **2. Create/Access Paper Trading Account**
- In the upper left corner, click on your account number
- Select "Open New Paper Account" or switch to existing paper account
- Your paper account will have a different account number (usually starts with different prefix)

### **3. Generate Paper Trading API Keys**
- While in your **paper trading account** (verify account number in top left)
- Go to "Account Settings" or "API Keys" section
- Click "Generate New API Key" or "Create API Key"
- **IMPORTANT**: Make sure you're in the paper account, not live account
- Copy the generated API Key and Secret Key

### **4. Update Environment Variables**
Replace the current Alpaca credentials in your `.env` file with the **paper trading** credentials:

```bash
# Alpaca Paper Trading API Credentials
ALPACA_API_KEY=YOUR_PAPER_TRADING_API_KEY_HERE
ALPACA_API_SECRET=YOUR_PAPER_TRADING_SECRET_KEY_HERE
ALPACA_BASE_URL=https://paper-api.alpaca.markets
```

### **5. Verify Paper Trading Setup**
- Paper trading accounts typically start with $100,000 virtual cash
- The account number should be different from your live account
- API endpoint should be `https://paper-api.alpaca.markets`

## **🔍 TROUBLESHOOTING COMMON ISSUES**

### **"Forbidden" Error**
- **Cause**: Using live trading API keys with paper trading endpoint
- **Solution**: Generate new API keys from within the paper trading account

### **"Account not authorized to trade"**
- **Cause**: Account may need to be reset or reactivated
- **Solution**: Reset paper account and regenerate API keys

### **"Request is not authorized"**
- **Cause**: API keys may be expired or incorrect
- **Solution**: Generate fresh API keys from paper trading dashboard

## **📊 VERIFICATION CHECKLIST**

Before proceeding with live paper trading:

- [ ] Logged into Alpaca dashboard
- [ ] Switched to paper trading account (check account number)
- [ ] Generated new API keys from paper account
- [ ] Updated `.env` file with paper trading credentials
- [ ] Verified base URL is `https://paper-api.alpaca.markets`
- [ ] Tested connection with `python test_alpaca_connection.py`
- [ ] Confirmed account shows $100,000 virtual cash
- [ ] Verified account type shows "Paper Trading"

## **🎯 EXPECTED RESULTS AFTER SETUP**

When properly configured, you should see:

```
✅ Alpaca connection successful!
📊 Account: PA123456789 (Paper Trading)
💰 Equity: $100,000.00
💵 Cash: $100,000.00
🔄 Buying Power: $200,000.00
📈 Portfolio Value: $100,000.00
📊 No current positions
```

## **🚨 SECURITY NOTES**

- **Never use live trading API keys for testing**
- Paper trading keys are safe to use for development
- Keep API keys secure and never commit them to version control
- Regenerate keys if you suspect they've been compromised

## **📞 SUPPORT RESOURCES**

If you continue to have issues:

1. **Alpaca Support**: [https://alpaca.markets/support](https://alpaca.markets/support)
2. **Alpaca Forum**: [https://forum.alpaca.markets](https://forum.alpaca.markets)
3. **Documentation**: [https://docs.alpaca.markets/docs/paper-trading](https://docs.alpaca.markets/docs/paper-trading)

## **⚡ QUICK TEST COMMAND**

After updating your credentials, test the connection:

```bash
cd /Users/<USER>/crypto
python test_alpaca_connection.py
```

Expected output should show successful connection to paper trading account with $100,000 virtual equity.

---

**🎯 NEXT STEPS**: Once Alpaca paper trading is properly configured, we can proceed with the live paper trading deployment and begin systematic testing of the QuantumEdge system with real market data and simulated trades.
