#!/usr/bin/env python3
"""
QuantumEdge Financial Intelligence Tool - Live Pipeline Demo
Demonstrates real-time data ingestion with validation and storage
"""

import asyncio
import logging
from datetime import datetime
from typing import List
import sys
import os

# Add src to path
sys.path.append('src')

from src.config import config
from src.rate_limiter import initialize_rate_limiter
from src.market_data_pipeline import MarketDataPipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('quantumedge.demo')

async def demo_single_symbol(symbol: str = 'AAPL'):
    """Demonstrate data ingestion for a single symbol"""
    logger.info(f"🚀 Starting single symbol demo for {symbol}")
    
    async with MarketDataPipeline() as pipeline:
        logger.info(f"📊 Ingesting data for {symbol}...")
        
        start_time = datetime.utcnow()
        result = await pipeline.ingest_symbol_data(symbol, days_back=7)
        end_time = datetime.utcnow()
        
        processing_time = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ Ingestion complete for {symbol}")
        logger.info(f"   Processing Time: {processing_time:.2f}s")
        logger.info(f"   Quality Score: {result.quality_score:.2f}")
        logger.info(f"   Validation Passed: {result.passed}")
        logger.info(f"   Record Count: {result.record_count}")
        
        # Display validation details
        if result.checks:
            logger.info("🔍 Validation Details:")
            for check in result.checks:
                status = "✅" if check.score >= 0.8 else "⚠️" if check.score >= 0.5 else "❌"
                logger.info(f"   {status} {check.check_name}: {check.score:.2f} - {check.message}")
        
        return result

async def demo_batch_symbols(symbols: List[str] = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']):
    """Demonstrate batch data ingestion"""
    logger.info(f"🚀 Starting batch demo for {len(symbols)} symbols: {', '.join(symbols)}")
    
    async with MarketDataPipeline() as pipeline:
        logger.info("📊 Ingesting batch data...")
        
        start_time = datetime.utcnow()
        results = await pipeline.ingest_batch_symbols(symbols, max_concurrent=3)
        end_time = datetime.utcnow()
        
        processing_time = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ Batch ingestion complete")
        logger.info(f"   Total Processing Time: {processing_time:.2f}s")
        logger.info(f"   Symbols Processed: {len(results)}")
        
        # Summary statistics
        if results:
            avg_quality = sum(r.quality_score for r in results) / len(results)
            passed_count = sum(1 for r in results if r.passed)
            
            logger.info(f"📈 Batch Statistics:")
            logger.info(f"   Average Quality Score: {avg_quality:.2f}")
            logger.info(f"   Validation Pass Rate: {passed_count}/{len(results)} ({passed_count/len(results)*100:.1f}%)")
            logger.info(f"   Processing Rate: {len(results)/processing_time:.1f} symbols/second")
            
            # Individual results
            for result in results:
                status = "✅" if result.passed else "❌"
                logger.info(f"   {status} {result.symbol}: Quality {result.quality_score:.2f}")
        
        return results

async def demo_pipeline_status():
    """Demonstrate pipeline status monitoring"""
    logger.info("🚀 Demonstrating pipeline status monitoring")
    
    async with MarketDataPipeline() as pipeline:
        status = await pipeline.get_pipeline_status()
        
        logger.info("📊 Pipeline Status:")
        logger.info(f"   Timestamp: {status['timestamp']}")
        
        if 'data_quality' in status:
            quality = status['data_quality']
            logger.info(f"   Data Quality Stats:")
            logger.info(f"     Total Validations: {quality.get('total_validations', 0)}")
            logger.info(f"     Average Quality Score: {quality.get('avg_quality_score', 0):.2f}")
            logger.info(f"     Pass Rate: {quality.get('pass_rate', 0)*100:.1f}%")
            logger.info(f"     Symbols Validated: {quality.get('symbols_validated', 0)}")
        
        if 'rate_limits' in status:
            logger.info(f"   Rate Limit Status:")
            for api, limits in status['rate_limits'].items():
                if isinstance(limits, dict):
                    logger.info(f"     {api.title()}: {limits}")
        
        return status

async def demo_performance_test():
    """Demonstrate performance capabilities"""
    logger.info("🚀 Starting performance demonstration")
    
    # Test data validation performance
    from src.data_validation import DataValidator
    
    validator = DataValidator()
    test_data = {
        'open': 150.0,
        'high': 155.0,
        'low': 149.0,
        'close': 152.0,
        'volume': 1000000,
        'timestamp': datetime.utcnow().timestamp()
    }
    
    logger.info("⚡ Testing validation performance...")
    
    start_time = datetime.utcnow()
    validation_count = 1000
    
    for i in range(validation_count):
        result = validator.validate_market_data(test_data, f'PERF{i}')
        assert result.quality_score >= 0.95
    
    end_time = datetime.utcnow()
    processing_time = (end_time - start_time).total_seconds()
    throughput = validation_count / processing_time
    
    logger.info(f"✅ Performance Test Results:")
    logger.info(f"   Validations: {validation_count}")
    logger.info(f"   Processing Time: {processing_time:.2f}s")
    logger.info(f"   Throughput: {throughput:.0f} validations/second")
    logger.info(f"   Target Met: {'✅' if throughput >= 100 else '❌'} (Target: 100/sec)")

async def demo_error_handling():
    """Demonstrate error handling and recovery"""
    logger.info("🚀 Demonstrating error handling")
    
    async with MarketDataPipeline() as pipeline:
        # Test with invalid symbol
        logger.info("🔍 Testing invalid symbol handling...")
        result = await pipeline.ingest_symbol_data('INVALID_SYMBOL_12345')
        
        logger.info(f"   Invalid Symbol Result:")
        logger.info(f"     Quality Score: {result.quality_score:.2f}")
        logger.info(f"     Passed: {result.passed}")
        logger.info(f"     Record Count: {result.record_count}")
        
        # Test with mixed valid/invalid symbols
        logger.info("🔍 Testing mixed symbol batch...")
        mixed_symbols = ['AAPL', 'INVALID123', 'MSFT', 'BADSTOCK456']
        results = await pipeline.ingest_batch_symbols(mixed_symbols, max_concurrent=2)
        
        valid_results = [r for r in results if r.passed]
        invalid_results = [r for r in results if not r.passed]
        
        logger.info(f"   Mixed Batch Results:")
        logger.info(f"     Total Symbols: {len(mixed_symbols)}")
        logger.info(f"     Valid Results: {len(valid_results)}")
        logger.info(f"     Invalid Results: {len(invalid_results)}")
        logger.info(f"     Success Rate: {len(valid_results)/len(results)*100:.1f}%")

async def main():
    """Main demo function"""
    logger.info("🎯 QuantumEdge Financial Intelligence Tool - Live Demo")
    logger.info("=" * 60)
    
    # Initialize rate limiter
    await initialize_rate_limiter()
    
    try:
        # Demo 1: Single symbol ingestion
        logger.info("\n" + "=" * 60)
        logger.info("DEMO 1: Single Symbol Data Ingestion")
        logger.info("=" * 60)
        await demo_single_symbol('AAPL')
        
        # Demo 2: Batch symbol ingestion
        logger.info("\n" + "=" * 60)
        logger.info("DEMO 2: Batch Symbol Data Ingestion")
        logger.info("=" * 60)
        await demo_batch_symbols(['AAPL', 'MSFT', 'GOOGL'])
        
        # Demo 3: Pipeline status monitoring
        logger.info("\n" + "=" * 60)
        logger.info("DEMO 3: Pipeline Status Monitoring")
        logger.info("=" * 60)
        await demo_pipeline_status()
        
        # Demo 4: Performance testing
        logger.info("\n" + "=" * 60)
        logger.info("DEMO 4: Performance Testing")
        logger.info("=" * 60)
        await demo_performance_test()
        
        # Demo 5: Error handling
        logger.info("\n" + "=" * 60)
        logger.info("DEMO 5: Error Handling & Recovery")
        logger.info("=" * 60)
        await demo_error_handling()
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 ALL DEMOS COMPLETED SUCCESSFULLY!")
        logger.info("=" * 60)
        
        # Final system status
        logger.info("\n📊 Final System Status:")
        logger.info(f"   Environment: {'Production Ready' if all(config.validate_api_keys().values()) else 'Development'}")
        logger.info(f"   APIs Validated: {sum(config.validate_api_keys().values())}/{len(config.validate_api_keys())}")
        logger.info(f"   Budget Status: ${config.budget['monthly_budget']:.2f} monthly budget")
        logger.info(f"   Hardware: M1 Max optimized")
        logger.info(f"   Performance: >100 validations/second")
        
    except Exception as e:
        logger.error(f"❌ Demo failed with error: {e}")
        raise

if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())
