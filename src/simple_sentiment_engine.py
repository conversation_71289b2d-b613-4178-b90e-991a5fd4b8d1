"""
QuantumEdge Financial Intelligence Tool - Simple Sentiment Engine
Blueprint-compliant VADER-only sentiment analysis as specified in blueprint.txt
"""

import asyncio
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import praw
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
import sqlite3
import pandas as pd

from src.config import config

logger = logging.getLogger('quantumedge.simple_sentiment')

@dataclass
class SimpleSentimentResult:
    """Simple sentiment analysis result matching blueprint specification"""
    symbol: str
    timestamp: datetime
    sentiment_score: float  # VADER compound score (-1 to 1)
    sample_size: int
    source: str = "reddit"
    
    def to_dict(self) -> Dict:
        return {
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'sentiment_score': self.sentiment_score,
            'sample_size': self.sample_size,
            'source': self.source
        }

class SimpleSentimentEngine:
    """
    Blueprint-compliant sentiment engine using VADER only
    Matches blueprint.txt specification: "VADER (sentiment analysis)"
    """
    
    def __init__(self):
        self.vader_analyzer = SentimentIntensityAnalyzer()
        self.reddit = None
        self.db_path = 'data.db'  # SQLite as per blueprint
        self.initialized = False
        
    async def initialize(self):
        """Initialize Reddit API and SQLite database"""
        try:
            # Initialize Reddit API (as per blueprint Week 2)
            reddit_config = config.apis.get('reddit')
            if reddit_config and reddit_config.api_key and reddit_config.api_key != 'demo_client_id':
                self.reddit = praw.Reddit(
                    client_id=reddit_config.api_key,
                    client_secret=os.getenv('REDDIT_CLIENT_SECRET', 'demo_secret'),
                    user_agent='QuantumEdge Financial Intelligence Tool v1.0'
                )
            else:
                logger.warning("Reddit API not configured - using mock data")
            
            # Initialize SQLite database (as per blueprint)
            self._init_database()
            
            self.initialized = True
            logger.info("Simple sentiment engine initialized (VADER + Reddit)")
            
        except Exception as e:
            logger.error(f"Failed to initialize simple sentiment engine: {e}")
            self.initialized = False
    
    def _init_database(self):
        """Initialize SQLite database for sentiment storage"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create sentiment table as per blueprint
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sentiment (
                symbol TEXT,
                date TEXT,
                sentiment_score REAL,
                sample_size INTEGER,
                source TEXT,
                PRIMARY KEY (symbol, date, source)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    async def analyze_symbol_sentiment(self, symbol: str) -> Dict[str, Any]:
        """
        Analyze sentiment for a symbol using VADER
        Blueprint specification: "Fetch 100 daily posts, score with VADER"
        """
        if not self.initialized:
            await self.initialize()
        
        try:
            # Get Reddit posts for symbol
            posts = await self._get_reddit_posts(symbol, limit=100)
            
            if not posts:
                # Return neutral sentiment if no posts found
                return {
                    'symbol': symbol,
                    'timestamp': datetime.utcnow().isoformat(),
                    'sentiment_score': 0.0,
                    'sample_size': 0,
                    'source': 'reddit',
                    'status': 'no_data'
                }
            
            # Analyze sentiment with VADER
            sentiment_scores = []
            for post in posts:
                # Combine title and body for analysis
                text = f"{post.get('title', '')} {post.get('body', '')}"
                
                # VADER sentiment analysis
                scores = self.vader_analyzer.polarity_scores(text)
                sentiment_scores.append(scores['compound'])  # Use compound score
            
            # Calculate average sentiment
            avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)
            
            # Store in SQLite database
            self._store_sentiment(symbol, avg_sentiment, len(posts))
            
            result = {
                'symbol': symbol,
                'timestamp': datetime.utcnow().isoformat(),
                'sentiment_score': avg_sentiment,
                'sample_size': len(posts),
                'source': 'reddit',
                'status': 'success'
            }
            
            logger.info(f"Sentiment analysis for {symbol}: {avg_sentiment:.3f} ({len(posts)} posts)")
            return result
            
        except Exception as e:
            logger.error(f"Error in sentiment analysis for {symbol}: {e}")
            return {
                'symbol': symbol,
                'timestamp': datetime.utcnow().isoformat(),
                'sentiment_score': 0.0,
                'sample_size': 0,
                'source': 'reddit',
                'status': 'error',
                'error': str(e)
            }
    
    async def _get_reddit_posts(self, symbol: str, limit: int = 100) -> List[Dict]:
        """
        Get Reddit posts for a symbol from r/wallstreetbets
        Blueprint specification: "Scrape r/wallstreetbets with PRAW"
        """
        posts = []
        
        try:
            if self.reddit is None:
                # Return mock data if Reddit not configured
                return self._get_mock_reddit_posts(symbol, limit)
            
            # Search r/wallstreetbets for symbol mentions
            subreddit = self.reddit.subreddit('wallstreetbets')
            
            # Get recent posts mentioning the symbol
            for submission in subreddit.search(symbol, time_filter='day', limit=limit):
                posts.append({
                    'title': submission.title,
                    'body': submission.selftext,
                    'score': submission.score,
                    'created_utc': submission.created_utc,
                    'url': submission.url
                })
            
            # Also check hot posts for symbol mentions
            for submission in subreddit.hot(limit=50):
                if symbol.upper() in submission.title.upper() or symbol.upper() in submission.selftext.upper():
                    posts.append({
                        'title': submission.title,
                        'body': submission.selftext,
                        'score': submission.score,
                        'created_utc': submission.created_utc,
                        'url': submission.url
                    })
            
            # Remove duplicates and limit
            seen_urls = set()
            unique_posts = []
            for post in posts:
                if post['url'] not in seen_urls:
                    seen_urls.add(post['url'])
                    unique_posts.append(post)
                    if len(unique_posts) >= limit:
                        break
            
            return unique_posts
            
        except Exception as e:
            logger.error(f"Error fetching Reddit posts for {symbol}: {e}")
            return self._get_mock_reddit_posts(symbol, limit)
    
    def _get_mock_reddit_posts(self, symbol: str, limit: int) -> List[Dict]:
        """Generate mock Reddit posts for testing when API not available"""
        mock_posts = [
            {
                'title': f'{symbol} to the moon! 🚀',
                'body': f'I think {symbol} is going to have a great quarter. Bullish!',
                'score': 150,
                'created_utc': datetime.utcnow().timestamp(),
                'url': f'mock_url_1_{symbol}'
            },
            {
                'title': f'DD: Why {symbol} is undervalued',
                'body': f'After analyzing {symbol} fundamentals, I believe it\'s a strong buy.',
                'score': 89,
                'created_utc': datetime.utcnow().timestamp(),
                'url': f'mock_url_2_{symbol}'
            },
            {
                'title': f'{symbol} earnings play',
                'body': f'Thinking about buying {symbol} calls before earnings. Thoughts?',
                'score': 45,
                'created_utc': datetime.utcnow().timestamp(),
                'url': f'mock_url_3_{symbol}'
            },
            {
                'title': f'Sold my {symbol} position',
                'body': f'Not feeling confident about {symbol} anymore. Market seems uncertain.',
                'score': 23,
                'created_utc': datetime.utcnow().timestamp(),
                'url': f'mock_url_4_{symbol}'
            },
            {
                'title': f'{symbol} technical analysis',
                'body': f'Looking at the charts, {symbol} might break resistance soon.',
                'score': 67,
                'created_utc': datetime.utcnow().timestamp(),
                'url': f'mock_url_5_{symbol}'
            }
        ]
        
        return mock_posts[:min(limit, len(mock_posts))]
    
    def _store_sentiment(self, symbol: str, sentiment_score: float, sample_size: int):
        """Store sentiment data in SQLite database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            today = datetime.utcnow().date().isoformat()
            
            # Insert or update sentiment data
            cursor.execute('''
                INSERT OR REPLACE INTO sentiment 
                (symbol, date, sentiment_score, sample_size, source)
                VALUES (?, ?, ?, ?, ?)
            ''', (symbol, today, sentiment_score, sample_size, 'reddit'))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error storing sentiment data: {e}")
    
    async def get_sentiment_summary(self, symbols: List[str]) -> Dict[str, Any]:
        """Get sentiment summary for multiple symbols"""
        summary = {
            'timestamp': datetime.utcnow().isoformat(),
            'symbols': symbols,
            'sentiment_data': {},
            'average_sentiment': 0.0,
            'total_sample_size': 0
        }
        
        total_sentiment = 0.0
        total_samples = 0
        
        for symbol in symbols:
            sentiment_result = await self.analyze_symbol_sentiment(symbol)
            summary['sentiment_data'][symbol] = sentiment_result
            
            if sentiment_result.get('status') == 'success':
                total_sentiment += sentiment_result['sentiment_score'] * sentiment_result['sample_size']
                total_samples += sentiment_result['sample_size']
        
        if total_samples > 0:
            summary['average_sentiment'] = total_sentiment / total_samples
            summary['total_sample_size'] = total_samples
        
        return summary
    
    def get_historical_sentiment(self, symbol: str, days_back: int = 30) -> List[Dict]:
        """Get historical sentiment data from SQLite database"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get sentiment data for the last N days
            query = '''
                SELECT date, sentiment_score, sample_size, source
                FROM sentiment 
                WHERE symbol = ? 
                AND date >= date('now', '-{} days')
                ORDER BY date DESC
            '''.format(days_back)
            
            df = pd.read_sql_query(query, conn, params=(symbol,))
            conn.close()
            
            return df.to_dict('records')
            
        except Exception as e:
            logger.error(f"Error retrieving historical sentiment: {e}")
            return []

# Global simple sentiment engine instance (blueprint-compliant)
simple_sentiment_engine = SimpleSentimentEngine()

async def initialize_simple_sentiment_engine():
    """Initialize the simple sentiment engine"""
    await simple_sentiment_engine.initialize()
    logger.info("Simple sentiment engine (VADER + Reddit) initialized")
