"""
QuantumEdge Financial Intelligence Tool - Sentiment Engine Tests
Comprehensive testing of sentiment analysis components
"""

import pytest
import asyncio
import sys
import os
from datetime import datetime

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.sentiment_engine import (
    SentimentEngine, VADERSentimentAnalyzer, NewsDataClient, 
    RedditSentimentClient, SentimentResult, NewsArticle
)

class TestVADERSentimentAnalyzer:
    """Test VADER sentiment analyzer"""
    
    def setup_method(self):
        self.analyzer = VADERSentimentAnalyzer()
    
    def test_positive_sentiment(self):
        """Test positive sentiment detection"""
        text = "Apple reports strong earnings beat, stock surges on bullish outlook"
        score, confidence, label = self.analyzer.analyze_sentiment(text)
        
        assert score > 0.1
        assert label == 'positive'
        assert confidence > 0.0
    
    def test_negative_sentiment(self):
        """Test negative sentiment detection"""
        text = "Stock crashes after disappointing earnings miss, bearish outlook ahead"
        score, confidence, label = self.analyzer.analyze_sentiment(text)
        
        assert score < -0.1
        assert label == 'negative'
        assert confidence > 0.0
    
    def test_neutral_sentiment(self):
        """Test neutral sentiment detection"""
        text = "Company reports quarterly results in line with expectations"
        score, confidence, label = self.analyzer.analyze_sentiment(text)
        
        assert -0.1 <= score <= 0.1
        assert label == 'neutral'
    
    def test_financial_lexicon(self):
        """Test financial-specific terms"""
        # Test bullish
        bullish_text = "Very bullish on this stock"
        score1, _, label1 = self.analyzer.analyze_sentiment(bullish_text)
        
        # Test bearish
        bearish_text = "Very bearish on this stock"
        score2, _, label2 = self.analyzer.analyze_sentiment(bearish_text)
        
        assert score1 > 0 and label1 == 'positive'
        assert score2 < 0 and label2 == 'negative'
    
    def test_empty_text(self):
        """Test handling of empty text"""
        score, confidence, label = self.analyzer.analyze_sentiment("")
        
        assert score == 0.0
        assert confidence == 0.0
        assert label == 'neutral'

class TestNewsDataClient:
    """Test news data client"""
    
    @pytest.mark.asyncio
    async def test_client_initialization(self):
        """Test news client initialization"""
        async with NewsDataClient() as client:
            assert client.session is not None
    
    def test_news_article_creation(self):
        """Test NewsArticle data structure"""
        article = NewsArticle(
            headline="Test Headline",
            summary="Test Summary",
            url="https://example.com",
            published_at=datetime.utcnow(),
            source="test",
            symbol="AAPL"
        )
        
        data_dict = article.to_dict()
        
        assert data_dict['headline'] == "Test Headline"
        assert data_dict['summary'] == "Test Summary"
        assert data_dict['url'] == "https://example.com"
        assert data_dict['source'] == "test"
        assert data_dict['symbol'] == "AAPL"

class TestRedditSentimentClient:
    """Test Reddit sentiment client"""
    
    def setup_method(self):
        self.client = RedditSentimentClient()
    
    @pytest.mark.asyncio
    async def test_reddit_initialization(self):
        """Test Reddit client initialization"""
        await self.client.initialize()
        # Should not raise any exceptions
    
    @pytest.mark.asyncio
    async def test_subreddit_sentiment_mock(self):
        """Test mock Reddit sentiment data"""
        posts = await self.client.get_subreddit_sentiment('AAPL', limit=5)
        
        assert isinstance(posts, list)
        assert len(posts) <= 5
        
        if posts:
            post = posts[0]
            assert 'title' in post
            assert 'selftext' in post
            assert 'created_utc' in post
            assert 'AAPL' in post['title'] or 'AAPL' in post['selftext']

class TestSentimentEngine:
    """Test main sentiment engine"""
    
    def setup_method(self):
        self.engine = SentimentEngine()
    
    @pytest.mark.asyncio
    async def test_engine_initialization(self):
        """Test sentiment engine initialization"""
        await self.engine.initialize()
        assert self.engine.sentiment_analyzer is not None
        assert self.engine.reddit_client is not None
    
    @pytest.mark.asyncio
    async def test_text_sentiment_analysis(self):
        """Test individual text sentiment analysis"""
        await self.engine.initialize()
        
        result = await self.engine._analyze_text_sentiment(
            "Great earnings report, very bullish outlook", 
            "test", 
            "AAPL"
        )
        
        assert isinstance(result, SentimentResult)
        assert result.sentiment_score > 0
        assert result.sentiment_label == 'positive'
        assert result.source == 'test'
        assert result.symbol == 'AAPL'
    
    @pytest.mark.asyncio
    async def test_symbol_sentiment_analysis(self):
        """Test comprehensive symbol sentiment analysis"""
        await self.engine.initialize()
        
        # This test uses mock data, so it should work without API keys
        result = await self.engine.analyze_symbol_sentiment('AAPL')
        
        assert result['symbol'] == 'AAPL'
        assert 'timestamp' in result
        assert 'news_sentiment' in result
        assert 'social_sentiment' in result
        assert 'overall_sentiment' in result
        
        overall = result['overall_sentiment']
        assert 'score' in overall
        assert 'confidence' in overall
        assert 'label' in overall
        assert 'sample_size' in overall
    
    @pytest.mark.asyncio
    async def test_sentiment_summary(self):
        """Test sentiment summary for multiple symbols"""
        await self.engine.initialize()
        
        symbols = ['AAPL', 'MSFT']
        summary = await self.engine.get_sentiment_summary(symbols)
        
        assert 'timestamp' in summary
        assert 'symbols_analyzed' in summary
        assert 'sentiment_distribution' in summary
        assert 'average_sentiment' in summary
        assert 'symbol_sentiments' in summary
        
        # Check sentiment distribution structure
        dist = summary['sentiment_distribution']
        assert 'positive' in dist
        assert 'negative' in dist
        assert 'neutral' in dist
    
    def test_text_cleaning(self):
        """Test text cleaning functionality"""
        dirty_text = "Check out this link: https://example.com/news!!! $AAPL to the moon 🚀🚀🚀"
        cleaned = self.engine._clean_text(dirty_text)
        
        # Should remove URLs and special characters
        assert 'https://example.com' not in cleaned
        assert '🚀' not in cleaned
        assert 'AAPL' in cleaned
    
    def test_score_to_label_conversion(self):
        """Test sentiment score to label conversion"""
        assert self.engine._score_to_label(0.5) == 'positive'
        assert self.engine._score_to_label(-0.5) == 'negative'
        assert self.engine._score_to_label(0.05) == 'neutral'
        assert self.engine._score_to_label(-0.05) == 'neutral'

class TestSentimentResult:
    """Test SentimentResult data structure"""
    
    def test_sentiment_result_creation(self):
        """Test SentimentResult creation and serialization"""
        result = SentimentResult(
            text="Test text",
            sentiment_score=0.75,
            confidence=0.85,
            sentiment_label='positive',
            source='test',
            timestamp=datetime.utcnow(),
            symbol='AAPL'
        )
        
        data_dict = result.to_dict()
        
        assert data_dict['text'] == "Test text"
        assert data_dict['sentiment_score'] == 0.75
        assert data_dict['confidence'] == 0.85
        assert data_dict['sentiment_label'] == 'positive'
        assert data_dict['source'] == 'test'
        assert data_dict['symbol'] == 'AAPL'
        assert 'timestamp' in data_dict

class TestIntegration:
    """Integration tests for sentiment engine"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_sentiment_analysis(self):
        """Test complete sentiment analysis workflow"""
        engine = SentimentEngine()
        await engine.initialize()
        
        # Test with a real symbol
        result = await engine.analyze_symbol_sentiment('AAPL')
        
        # Validate structure
        assert isinstance(result, dict)
        assert result['symbol'] == 'AAPL'
        
        # Should have some sentiment data (from mock sources)
        total_sentiments = len(result['news_sentiment']) + len(result['social_sentiment'])
        assert total_sentiments >= 0  # May be 0 if APIs are rate limited
        
        # Overall sentiment should be calculated
        overall = result['overall_sentiment']
        assert isinstance(overall['score'], (int, float))
        assert isinstance(overall['confidence'], (int, float))
        assert overall['label'] in ['positive', 'negative', 'neutral']
    
    @pytest.mark.asyncio
    async def test_performance_requirements(self):
        """Test sentiment analysis performance"""
        engine = SentimentEngine()
        await engine.initialize()
        
        start_time = datetime.utcnow()
        
        # Analyze sentiment for multiple texts
        test_texts = [
            "Strong earnings beat expectations",
            "Disappointing quarterly results",
            "Company maintains guidance",
            "Bullish outlook for next quarter",
            "Bearish sentiment in the market"
        ]
        
        results = []
        for text in test_texts:
            result = await engine._analyze_text_sentiment(text, 'test')
            results.append(result)
        
        end_time = datetime.utcnow()
        processing_time = (end_time - start_time).total_seconds()
        
        # Should process quickly
        assert processing_time < 5.0  # 5 seconds max
        assert len(results) == len(test_texts)
        
        # All results should be valid
        for result in results:
            assert isinstance(result.sentiment_score, (int, float))
            assert -1.0 <= result.sentiment_score <= 1.0
            assert result.sentiment_label in ['positive', 'negative', 'neutral']

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
