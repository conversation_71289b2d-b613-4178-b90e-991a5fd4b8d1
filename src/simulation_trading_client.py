"""
QuantumEdge Financial Intelligence Tool - Simulation Trading Client
Realistic trading simulation using real market data for testing and validation
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import pandas as pd
import numpy as np
import yfinance as yf

from src.alpaca_trading_client import TradingSignal, TradeExecution

logger = logging.getLogger('quantumedge.simulation')

@dataclass
class SimulatedAccount:
    """Simulated trading account"""
    account_number: str
    equity: float
    cash: float
    buying_power: float
    portfolio_value: float
    positions: List[Dict]
    
    def to_dict(self) -> Dict:
        return {
            'account_number': self.account_number,
            'equity': self.equity,
            'cash': self.cash,
            'buying_power': self.buying_power,
            'portfolio_value': self.portfolio_value,
            'positions_count': len(self.positions),
            'positions': self.positions
        }

@dataclass
class SimulatedPosition:
    """Simulated position"""
    symbol: str
    qty: float
    market_value: float
    avg_cost: float
    unrealized_pl: float
    unrealized_plpc: float
    
    def to_dict(self) -> Dict:
        return {
            'symbol': self.symbol,
            'qty': self.qty,
            'market_value': self.market_value,
            'unrealized_pl': self.unrealized_pl,
            'unrealized_plpc': self.unrealized_plpc
        }

class SimulationTradingClient:
    """
    Realistic trading simulation client that uses real market data
    Provides same interface as Alpaca client but simulates execution
    """
    
    def __init__(self):
        self.initialized = False
        
        # Account simulation
        self.account = SimulatedAccount(
            account_number="SIM123456789",
            equity=100000.0,
            cash=100000.0,
            buying_power=200000.0,  # 2:1 margin
            portfolio_value=100000.0,
            positions=[]
        )
        
        # Trading parameters
        self.max_position_size = 500.0
        self.stop_loss_pct = 0.02
        self.take_profit_pct = 0.04
        
        # Tracking
        self.orders = []
        self.order_counter = 1000
        self.trades_executed = []
        
        # Market data cache
        self.price_cache = {}
        self.last_cache_update = {}
    
    async def initialize(self):
        """Initialize simulation client"""
        try:
            logger.info("Initializing simulation trading client...")
            
            # Test market data access
            test_data = await self.get_current_price('AAPL')
            if test_data is None:
                raise Exception("Cannot access market data for simulation")
            
            self.initialized = True
            logger.info(f"Simulation client initialized with ${self.account.equity:,.2f} virtual capital")
            
        except Exception as e:
            logger.error(f"Failed to initialize simulation client: {e}")
            self.initialized = False
            raise
    
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price using yfinance"""
        try:
            # Check cache first (cache for 1 minute)
            now = datetime.utcnow()
            if (symbol in self.price_cache and 
                symbol in self.last_cache_update and
                (now - self.last_cache_update[symbol]).seconds < 60):
                return self.price_cache[symbol]
            
            # Fetch current price
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="1d", interval="1m")
            
            if hist.empty:
                logger.warning(f"No price data available for {symbol}")
                return None
            
            current_price = float(hist['Close'].iloc[-1])
            
            # Update cache
            self.price_cache[symbol] = current_price
            self.last_cache_update[symbol] = now
            
            return current_price
            
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            return None
    
    async def get_account_info(self) -> Dict[str, Any]:
        """Get simulated account information"""
        if not self.initialized:
            await self.initialize()
        
        try:
            # Update portfolio value based on current positions
            await self._update_portfolio_value()
            
            return self.account.to_dict()
            
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {'error': str(e)}
    
    async def _update_portfolio_value(self):
        """Update portfolio value based on current market prices"""
        try:
            total_position_value = 0.0
            
            for position in self.account.positions:
                current_price = await self.get_current_price(position['symbol'])
                if current_price:
                    market_value = position['qty'] * current_price
                    position['market_value'] = market_value
                    position['unrealized_pl'] = market_value - (position['qty'] * position['avg_cost'])
                    position['unrealized_plpc'] = position['unrealized_pl'] / (position['qty'] * position['avg_cost'])
                    total_position_value += market_value
            
            self.account.portfolio_value = self.account.cash + total_position_value
            self.account.equity = self.account.portfolio_value
            
        except Exception as e:
            logger.error(f"Error updating portfolio value: {e}")
    
    async def validate_trading_signal(self, signal: TradingSignal) -> Tuple[bool, str]:
        """Validate trading signal against account constraints"""
        try:
            # Check basic signal criteria
            if signal.confidence < 0.7:
                return False, f"Confidence too low: {signal.confidence:.3f} < 0.7"
            
            if signal.sentiment_score < 0.6:
                return False, f"Sentiment too low: {signal.sentiment_score:.3f} < 0.6"
            
            if signal.risk_score > 0.05:
                return False, f"Risk too high: {signal.risk_score:.3f} > 0.05"
            
            # Check account constraints
            if signal.action == 'BUY':
                position_value = signal.position_size or self.max_position_size
                if self.account.cash < position_value:
                    return False, f"Insufficient cash: ${self.account.cash:.2f} < ${position_value:.2f}"
            
            # Check for existing position
            existing_position = None
            for pos in self.account.positions:
                if pos['symbol'] == signal.symbol:
                    existing_position = pos
                    break
            
            if signal.action == 'BUY' and existing_position and existing_position['qty'] > 0:
                return False, f"Already have long position in {signal.symbol}"
            
            if signal.action == 'SELL' and not existing_position:
                return False, f"No position to sell in {signal.symbol}"
            
            return True, "Signal validated"
            
        except Exception as e:
            return False, f"Validation error: {e}"
    
    async def execute_trade(self, signal: TradingSignal) -> TradeExecution:
        """Execute trading signal with realistic simulation"""
        execution_time = datetime.utcnow()
        
        try:
            # Validate signal
            is_valid, reason = await self.validate_trading_signal(signal)
            if not is_valid:
                return TradeExecution(
                    signal=signal,
                    order_id=None,
                    execution_price=None,
                    execution_time=execution_time,
                    status='REJECTED',
                    error_message=reason
                )
            
            # Get current price
            current_price = await self.get_current_price(signal.symbol)
            if not current_price:
                return TradeExecution(
                    signal=signal,
                    order_id=None,
                    execution_price=None,
                    execution_time=execution_time,
                    status='FAILED',
                    error_message=f"Cannot get current price for {signal.symbol}"
                )
            
            # Simulate realistic execution price (add small slippage)
            slippage = np.random.normal(0, 0.001)  # 0.1% average slippage
            execution_price = current_price * (1 + slippage)
            
            # Calculate position size
            position_value = signal.position_size or self.max_position_size
            qty = position_value / execution_price
            
            # Generate order ID
            order_id = f"SIM{self.order_counter}"
            self.order_counter += 1
            
            # Execute the trade
            if signal.action == 'BUY':
                await self._execute_buy(signal.symbol, qty, execution_price)
            elif signal.action == 'SELL':
                await self._execute_sell(signal.symbol, qty, execution_price)
            
            # Create execution record
            execution = TradeExecution(
                signal=signal,
                order_id=order_id,
                execution_price=execution_price,
                execution_time=execution_time,
                status='FILLED'  # Simulate immediate fill
            )
            
            self.trades_executed.append(execution)
            
            logger.info(f"✅ Simulated trade executed: {signal.action} {qty:.3f} shares of {signal.symbol} at ${execution_price:.2f}")
            
            return execution
            
        except Exception as e:
            logger.error(f"Error executing simulated trade: {e}")
            return TradeExecution(
                signal=signal,
                order_id=None,
                execution_price=None,
                execution_time=execution_time,
                status='FAILED',
                error_message=str(e)
            )
    
    async def _execute_buy(self, symbol: str, qty: float, price: float):
        """Execute buy order simulation"""
        cost = qty * price
        
        # Update cash
        self.account.cash -= cost
        
        # Update or create position
        existing_position = None
        for i, pos in enumerate(self.account.positions):
            if pos['symbol'] == symbol:
                existing_position = i
                break
        
        if existing_position is not None:
            # Update existing position
            pos = self.account.positions[existing_position]
            total_qty = pos['qty'] + qty
            total_cost = (pos['qty'] * pos['avg_cost']) + cost
            new_avg_cost = total_cost / total_qty
            
            pos['qty'] = total_qty
            pos['avg_cost'] = new_avg_cost
        else:
            # Create new position
            new_position = {
                'symbol': symbol,
                'qty': qty,
                'avg_cost': price,
                'market_value': cost,
                'unrealized_pl': 0.0,
                'unrealized_plpc': 0.0
            }
            self.account.positions.append(new_position)
    
    async def _execute_sell(self, symbol: str, qty: float, price: float):
        """Execute sell order simulation"""
        proceeds = qty * price
        
        # Update cash
        self.account.cash += proceeds
        
        # Update position
        for i, pos in enumerate(self.account.positions):
            if pos['symbol'] == symbol:
                if pos['qty'] >= qty:
                    pos['qty'] -= qty
                    if pos['qty'] == 0:
                        # Remove position if fully closed
                        self.account.positions.pop(i)
                break
    
    async def get_portfolio_performance(self) -> Dict[str, Any]:
        """Calculate portfolio performance metrics"""
        try:
            await self._update_portfolio_value()
            
            # Calculate P&L
            total_pl = self.account.equity - 100000.0  # Initial capital
            total_pl_pct = (total_pl / 100000.0) * 100
            
            # Position metrics
            winning_positions = len([pos for pos in self.account.positions if pos['unrealized_pl'] > 0])
            losing_positions = len([pos for pos in self.account.positions if pos['unrealized_pl'] < 0])
            total_positions = len(self.account.positions)
            
            win_rate = (winning_positions / total_positions * 100) if total_positions > 0 else 0
            
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'total_equity': self.account.equity,
                'total_pl': total_pl,
                'total_pl_pct': total_pl_pct,
                'total_positions': total_positions,
                'winning_positions': winning_positions,
                'losing_positions': losing_positions,
                'win_rate': win_rate,
                'trades_executed': len(self.trades_executed),
                'account_status': {
                    'buying_power': self.account.buying_power,
                    'cash': self.account.cash,
                    'portfolio_value': self.account.portfolio_value
                }
            }
            
        except Exception as e:
            logger.error(f"Error calculating portfolio performance: {e}")
            return {'error': str(e)}
    
    async def close_position(self, symbol: str, reason: str = "Manual close") -> Dict[str, Any]:
        """Close a position in simulation"""
        try:
            # Find position
            position = None
            for pos in self.account.positions:
                if pos['symbol'] == symbol:
                    position = pos
                    break
            
            if not position:
                return {'error': f'No position found for {symbol}'}
            
            # Get current price
            current_price = await self.get_current_price(symbol)
            if not current_price:
                return {'error': f'Cannot get current price for {symbol}'}
            
            # Execute sell
            await self._execute_sell(symbol, position['qty'], current_price)
            
            logger.info(f"Position closed for {symbol}: {reason}")
            
            return {
                'symbol': symbol,
                'qty_closed': position['qty'],
                'market_value': position['market_value'],
                'unrealized_pl': position['unrealized_pl'],
                'reason': reason,
                'close_order_id': f"SIM{self.order_counter}"
            }
            
        except Exception as e:
            logger.error(f"Error closing position for {symbol}: {e}")
            return {'error': str(e)}

# Global simulation client
simulation_client = SimulationTradingClient()

async def initialize_simulation_trading():
    """Initialize simulation trading client"""
    await simulation_client.initialize()
    logger.info("Simulation trading client initialized")
